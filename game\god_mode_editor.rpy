# -*- coding: utf-8 -*-
# 上帝模式编辑器界面

# 上帝模式主界面
screen god_mode_editor_screen():
    tag menu
    
    # 背景
    add "#0f1419"
    
    # 标题栏
    frame:
        xalign 0.5
        ypos 10
        xsize 1000
        padding (20, 10)
        background "#2c3e50"
        
        hbox:
            spacing 30
            text "上帝模式编辑器" size 28 color "#e74c3c"
            text "当前时间: [current_time_info.get('time_string', '')]" size 14 color "#bdc3c7"
            text "模式: 全知全能" size 14 color "#f39c12"
    
    # 主要编辑区域
    hbox:
        xalign 0.5
        ypos 70
        spacing 20
        
        # 左侧：编辑工具栏
        frame:
            xsize 250
            ysize 600
            padding (15, 15)
            background "#34495e"
            
            vbox:
                spacing 15
                
                text "编辑工具" size 20 color "#3498db"
                
                # NPC编辑
                frame:
                    padding (10, 10)
                    background "#2c3e50"
                    
                    vbox:
                        spacing 8
                        text "NPC管理" size 16 color "#e67e22"
                        textbutton "编辑NPC" action SetVariable("editor_mode", "npc_edit") text_size 12
                        textbutton "创建NPC" action SetVariable("editor_mode", "npc_create") text_size 12
                        textbutton "批量编辑" action SetVariable("editor_mode", "npc_batch") text_size 12
                
                # 世界编辑
                frame:
                    padding (10, 10)
                    background "#2c3e50"
                    
                    vbox:
                        spacing 8
                        text "世界管理" size 16 color "#e67e22"
                        textbutton "时间控制" action SetVariable("editor_mode", "time_edit") text_size 12
                        textbutton "经济调整" action SetVariable("editor_mode", "economy_edit") text_size 12
                        textbutton "事件创建" action SetVariable("editor_mode", "event_create") text_size 12
                
                # 系统工具
                frame:
                    padding (10, 10)
                    background "#2c3e50"
                    
                    vbox:
                        spacing 8
                        text "系统工具" size 16 color "#e67e22"
                        textbutton "数据检查" action SetVariable("editor_mode", "data_inspect") text_size 12
                        textbutton "性能监控" action SetVariable("editor_mode", "performance") text_size 12
                        textbutton "编辑历史" action SetVariable("editor_mode", "history") text_size 12
                
                # 快速操作
                null height 20
                text "快速操作" size 16 color "#f39c12"
                textbutton "撤销 (Ctrl+Z)" action Function(god_mode_undo) text_size 12
                textbutton "重做 (Ctrl+Y)" action Function(god_mode_redo) text_size 12
                textbutton "保存状态" action Function(save_world_state) text_size 12
                textbutton "重新加载" action Function(reload_world_data) text_size 12
        
        # 中间：主编辑区域
        frame:
            xsize 500
            ysize 600
            padding (20, 20)
            background "#34495e"
            
            # 根据编辑模式显示不同内容
            if editor_mode == "npc_edit":
                use npc_editor_panel
            elif editor_mode == "npc_create":
                use npc_creator_panel
            elif editor_mode == "time_edit":
                use time_editor_panel
            elif editor_mode == "economy_edit":
                use economy_editor_panel
            elif editor_mode == "event_create":
                use event_creator_panel
            elif editor_mode == "data_inspect":
                use data_inspector_panel
            elif editor_mode == "history":
                use history_panel
            else:
                # 默认欢迎界面
                vbox:
                    spacing 20
                    text "欢迎使用上帝模式编辑器" size 24 color "#3498db" xalign 0.5
                    
                    text "在这里你可以：" size 16 color "#ecf0f1"
                    text "• 编辑任何NPC的属性和状态" size 14 color "#bdc3c7"
                    text "• 控制时间流逝和季节变化" size 14 color "#bdc3c7"
                    text "• 调整经济参数和市场状况" size 14 color "#bdc3c7"
                    text "• 创建自定义事件和新闻" size 14 color "#bdc3c7"
                    text "• 监控系统性能和数据状态" size 14 color "#bdc3c7"
                    
                    null height 20
                    text "选择左侧的工具开始编辑" size 16 color "#f39c12" xalign 0.5
        
        # 右侧：实时监控面板
        frame:
            xsize 250
            ysize 600
            padding (15, 15)
            background "#34495e"
            
            vbox:
                spacing 15
                
                text "实时监控" size 20 color "#3498db"
                
                # 系统状态
                frame:
                    padding (10, 10)
                    background "#2c3e50"
                    
                    vbox:
                        spacing 5
                        text "系统状态" size 14 color "#e67e22"
                        
                        if system_stats:
                            text "NPC数量: [system_stats.get('npc_count', 0)]" size 12 color "#bdc3c7"
                            text "活跃NPC: [system_stats.get('active_npcs', 0)]" size 12 color "#bdc3c7"
                            text "今日交易: [system_stats.get('daily_transactions', 0)]" size 12 color "#bdc3c7"
                            text "市场事件: [system_stats.get('market_events', 0)]" size 12 color "#bdc3c7"
                        else:
                            text "加载中..." size 12 color "#95a5a6"
                
                # 经济状况
                frame:
                    padding (10, 10)
                    background "#2c3e50"
                    
                    vbox:
                        spacing 5
                        text "经济状况" size 14 color "#e67e22"
                        
                        if economic_stats:
                            text "总现金: ¥[int(economic_stats.get('total_cash', 0))]" size 12 color "#bdc3c7"
                            text "供需比: [economic_stats.get('supply_demand_ratio', 0):.2f]" size 12 color "#bdc3c7"
                            text "价格变化: [economic_stats.get('avg_price_change', 0):.1f]%" size 12 color "#bdc3c7"
                            text "市场活跃度: [economic_stats.get('market_activity', '')]" size 12 color "#bdc3c7"
                        else:
                            text "加载中..." size 12 color "#95a5a6"
                
                # 最近操作
                frame:
                    padding (10, 10)
                    background "#2c3e50"
                    
                    vbox:
                        spacing 5
                        text "最近操作" size 14 color "#e67e22"
                        
                        viewport:
                            xsize 210
                            ysize 150
                            scrollbars "vertical"
                            mousewheel True
                            
                            vbox:
                                spacing 3
                                
                                if recent_operations:
                                    for op in recent_operations[-10:]:
                                        text "• [op]" size 10 color "#95a5a6"
                                else:
                                    text "暂无操作记录" size 12 color "#95a5a6"
                
                # 刷新按钮
                textbutton "刷新数据" action Function(refresh_god_mode_data) text_size 12
    
    # 底部状态栏
    frame:
        xalign 0.5
        ypos 680
        xsize 1000
        padding (15, 8)
        background "#2c3e50"
        
        hbox:
            spacing 30
            
            text "编辑模式: [editor_mode]" size 12 color "#bdc3c7"
            if unsaved_changes:
                text "未保存更改: [unsaved_changes]" size 12 color "#e74c3c"
            else:
                text "未保存更改: [unsaved_changes]" size 12 color "#27ae60"
            text "撤销栈: [len(undo_stack)]" size 12 color "#bdc3c7"
            text "重做栈: [len(redo_stack)]" size 12 color "#bdc3c7"
            
            textbutton "角色模式" action Jump("character_selection") text_size 12
            textbutton "返回世界" action Jump("world_browser") text_size 12

# NPC编辑面板
screen npc_editor_panel():
    vbox:
        spacing 15
        
        text "NPC编辑器" size 20 color "#3498db"
        
        # NPC选择
        frame:
            padding (10, 10)
            background "#2c3e50"
            
            vbox:
                spacing 10
                text "选择NPC" size 14 color "#f39c12"
                
                viewport:
                    xsize 460
                    ysize 150
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 5
                        
                        if available_npcs:
                            for npc in available_npcs:
                                textbutton "[npc.name] (ID: [npc.id[:8]]...)" action SetVariable("selected_npc_for_edit", npc) text_size 12
                        else:
                            text "没有可用的NPC" size 12 color "#95a5a6"
        
        # NPC属性编辑
        if selected_npc_for_edit:
            frame:
                padding (10, 10)
                background "#2c3e50"
                
                vbox:
                    spacing 10
                    text "编辑 [selected_npc_for_edit.name]" size 14 color "#f39c12"
                    
                    # 基本属性
                    hbox:
                        spacing 10
                        text "姓名:" size 12 color "#bdc3c7" xsize 60
                        input default selected_npc_for_edit.name length 20 size 12 color "#ecf0f1" changed npc_name_changed
                    
                    hbox:
                        spacing 10
                        text "年龄:" size 12 color "#bdc3c7" xsize 60
                        input default str(selected_npc_for_edit.age) length 3 size 12 color "#ecf0f1" changed npc_age_changed
                    
                    hbox:
                        spacing 10
                        text "现金:" size 12 color "#bdc3c7" xsize 60
                        input default str(int(selected_npc_for_edit.cash)) length 10 size 12 color "#ecf0f1" changed npc_cash_changed
                    
                    # 状态调整
                    text "状态调整:" size 12 color "#f39c12"
                    
                    hbox:
                        spacing 10
                        text "能量:" size 10 color "#bdc3c7" xsize 50
                        bar value FieldValue(selected_npc_for_edit.daily_stats, "energy") range 100 xsize 150 thumb "gui/slider/horizontal_hover_thumb.png"
                        text "[int(selected_npc_for_edit.daily_stats.energy)]" size 10 color "#f39c12"
                    
                    hbox:
                        spacing 10
                        text "幸福:" size 10 color "#bdc3c7" xsize 50
                        bar value FieldValue(selected_npc_for_edit.daily_stats, "happiness") range 100 xsize 150 thumb "gui/slider/horizontal_hover_thumb.png"
                        text "[int(selected_npc_for_edit.daily_stats.happiness)]" size 10 color "#e67e22"
                    
                    # 操作按钮
                    hbox:
                        spacing 10
                        textbutton "保存更改" action Function(save_npc_changes) text_size 12
                        textbutton "重置" action Function(reset_npc_changes) text_size 12
                        textbutton "删除NPC" action Function(delete_selected_npc) text_size 12

# 时间编辑面板
screen time_editor_panel():
    vbox:
        spacing 15
        
        text "时间控制器" size 20 color "#3498db"
        
        frame:
            padding (15, 15)
            background "#2c3e50"
            
            vbox:
                spacing 15
                
                # 当前时间显示
                text "当前时间: [current_time_info.get('time_string', '')]" size 14 color "#f39c12"
                
                # 时间调整
                hbox:
                    spacing 15
                    vbox:
                        spacing 5
                        text "天数:" size 12 color "#bdc3c7"
                        input default str(current_time_info.get('day', 1)) length 5 size 12 color "#ecf0f1" changed time_day_changed
                    
                    vbox:
                        spacing 5
                        text "小时:" size 12 color "#bdc3c7"
                        input default str(current_time_info.get('hour', 8)) length 2 size 12 color "#ecf0f1" changed time_hour_changed
                    
                    vbox:
                        spacing 5
                        text "年份:" size 12 color "#bdc3c7"
                        input default str(current_time_info.get('year', 1)) length 4 size 12 color "#ecf0f1" changed time_year_changed
                
                # 季节选择
                text "季节:" size 12 color "#bdc3c7"
                hbox:
                    spacing 10
                    textbutton "春季" action Function(set_season, "spring") text_size 12
                    textbutton "夏季" action Function(set_season, "summer") text_size 12
                    textbutton "秋季" action Function(set_season, "autumn") text_size 12
                    textbutton "冬季" action Function(set_season, "winter") text_size 12
                
                # 时间控制
                text "时间控制:" size 12 color "#f39c12"
                hbox:
                    spacing 10
                    textbutton "推进1小时" action Function(advance_time, 1) text_size 12
                    textbutton "推进1天" action Function(advance_time, 24) text_size 12
                    textbutton "推进1周" action Function(advance_time, 168) text_size 12
                
                textbutton "应用时间更改" action Function(apply_time_changes) text_size 14

# 全局变量和函数
init python:
    editor_mode = "welcome"
    selected_npc_for_edit = None
    available_npcs = []
    system_stats = {}
    economic_stats = {}
    recent_operations = []
    unsaved_changes = 0
    undo_stack = []
    redo_stack = []
    god_mode_editor = None
    
    def init_god_mode_editor():
        global god_mode_editor, db_manager
        if not db_manager:
            return False
        
        try:
            from core.god_mode_editor import GodModeEditor
            from core.time_system import TimeSystem
            
            time_sys = TimeSystem(db_manager)
            god_mode_editor = GodModeEditor(db_manager, time_sys)
            return True
        except Exception as e:
            print(f"上帝模式编辑器初始化失败: {e}")
            return False
    
    def refresh_god_mode_data():
        global available_npcs, system_stats, economic_stats, current_time_info
        
        if not god_mode_editor:
            if not init_god_mode_editor():
                renpy.notify("编辑器初始化失败")
                return
        
        try:
            # 刷新NPC列表
            available_npcs = db_manager.load_all_active_npcs()
            
            # 刷新系统统计
            system_stats = {
                "npc_count": len(available_npcs),
                "active_npcs": len([npc for npc in available_npcs if npc.daily_stats.energy > 20]),
                "daily_transactions": god_mode_editor.time_system.economy_system.daily_transaction_count,
                "market_events": len(god_mode_editor.time_system.market_event_system.active_events)
            }
            
            # 刷新经济统计
            economic_stats = god_mode_editor.time_system.economy_system.get_economic_summary()
            
            # 刷新时间信息
            current_time_info = god_mode_editor.time_system.get_current_time_info()
            
            renpy.notify("数据已刷新")
            
        except Exception as e:
            renpy.notify(f"刷新失败: {e}")
    
    def save_npc_changes():
        global selected_npc_for_edit, god_mode_editor, unsaved_changes
        
        if not selected_npc_for_edit or not god_mode_editor:
            renpy.notify("系统错误")
            return
        
        try:
            # 构建更改数据
            changes = {
                "name": selected_npc_for_edit.name,
                "age": selected_npc_for_edit.age,
                "cash": selected_npc_for_edit.cash,
                "daily_stats": {
                    "energy": selected_npc_for_edit.daily_stats.energy,
                    "happiness": selected_npc_for_edit.daily_stats.happiness,
                    "stress": selected_npc_for_edit.daily_stats.stress,
                    "health": selected_npc_for_edit.daily_stats.health,
                    "hunger": selected_npc_for_edit.daily_stats.hunger
                }
            }
            
            # 执行编辑
            success, message = god_mode_editor.edit_npc(selected_npc_for_edit.id, changes)
            
            if success:
                recent_operations.append(f"编辑NPC: {selected_npc_for_edit.name}")
                unsaved_changes = max(0, unsaved_changes - 1)
                renpy.notify(message)
            else:
                renpy.notify(f"保存失败: {message}")
                
        except Exception as e:
            renpy.notify(f"保存失败: {e}")
    
    def apply_time_changes():
        global god_mode_editor, current_time_info
        
        if not god_mode_editor:
            renpy.notify("编辑器未初始化")
            return
        
        try:
            # 这里应该从输入框获取新的时间值
            # 简化处理，直接使用当前值
            changes = {
                "day": current_time_info.get("day", 1),
                "hour": current_time_info.get("hour", 8),
                "year": current_time_info.get("year", 1)
            }
            
            success, message = god_mode_editor.edit_time(changes)
            
            if success:
                recent_operations.append("修改时间设置")
                refresh_god_mode_data()
                renpy.notify(message)
            else:
                renpy.notify(f"时间修改失败: {message}")
                
        except Exception as e:
            renpy.notify(f"时间修改失败: {e}")
    
    def set_season(season):
        global god_mode_editor
        
        if not god_mode_editor:
            renpy.notify("编辑器未初始化")
            return
        
        try:
            success, message = god_mode_editor.edit_time({"season": season})
            
            if success:
                recent_operations.append(f"设置季节为{season}")
                refresh_god_mode_data()
                renpy.notify(f"季节已设置为{season}")
            else:
                renpy.notify(f"季节设置失败: {message}")
                
        except Exception as e:
            renpy.notify(f"季节设置失败: {e}")
    
    def advance_time(hours):
        global god_mode_editor
        
        if not god_mode_editor:
            renpy.notify("编辑器未初始化")
            return
        
        try:
            results = god_mode_editor.time_system.advance_time(hours)
            
            recent_operations.append(f"推进时间{hours}小时")
            refresh_god_mode_data()
            
            # 显示结果摘要
            summary = []
            if results.get("completed_activities"):
                summary.append(f"完成活动: {len(results['completed_activities'])}")
            if results.get("economic_updates"):
                summary.append(f"经济更新: {len(results['economic_updates'])}")
            if results.get("news_generated"):
                summary.append(f"生成新闻: {len(results['news_generated'])}")
            
            if summary:
                renpy.notify(f"时间推进完成 - {', '.join(summary)}")
            else:
                renpy.notify(f"时间推进{hours}小时完成")
                
        except Exception as e:
            renpy.notify(f"时间推进失败: {e}")
    
    def god_mode_undo():
        global god_mode_editor
        
        if not god_mode_editor:
            renpy.notify("编辑器未初始化")
            return
        
        try:
            success, message = god_mode_editor.undo()
            
            if success:
                recent_operations.append("撤销操作")
                refresh_god_mode_data()
                renpy.notify(message)
            else:
                renpy.notify(message)
                
        except Exception as e:
            renpy.notify(f"撤销失败: {e}")
    
    def god_mode_redo():
        global god_mode_editor
        
        if not god_mode_editor:
            renpy.notify("编辑器未初始化")
            return
        
        try:
            success, message = god_mode_editor.redo()
            
            if success:
                recent_operations.append("重做操作")
                refresh_god_mode_data()
                renpy.notify(message)
            else:
                renpy.notify(message)
                
        except Exception as e:
            renpy.notify(f"重做失败: {e}")
    
    # 输入框回调函数
    def npc_name_changed(new_name):
        global selected_npc_for_edit, unsaved_changes
        if selected_npc_for_edit:
            selected_npc_for_edit.name = new_name
            unsaved_changes += 1
    
    def npc_age_changed(new_age):
        global selected_npc_for_edit, unsaved_changes
        if selected_npc_for_edit:
            try:
                selected_npc_for_edit.age = int(new_age)
                unsaved_changes += 1
            except ValueError:
                pass
    
    def npc_cash_changed(new_cash):
        global selected_npc_for_edit, unsaved_changes
        if selected_npc_for_edit:
            try:
                selected_npc_for_edit.cash = float(new_cash)
                unsaved_changes += 1
            except ValueError:
                pass

# 上帝模式编辑器入口
label god_mode_editor:
    call init_system
    
    python:
        if not init_god_mode_editor():
            renpy.notify("上帝模式编辑器初始化失败")
            renpy.jump("world_browser")
        
        refresh_god_mode_data()
    
    call screen god_mode_editor_screen
    
    return

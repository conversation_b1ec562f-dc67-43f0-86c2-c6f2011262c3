# -*- coding: utf-8 -*-
# 角色体验模式界面

# 角色选择界面
screen character_selection_screen():
    tag menu
    
    # 背景
    add "#1a252f"
    
    # 标题
    frame:
        xalign 0.5
        ypos 20
        padding (20, 10)
        background "#34495e"
        
        text "角色选择" size 30 color "#ecf0f1" xalign 0.5
    
    # 主要内容
    hbox:
        xalign 0.5
        yalign 0.5
        spacing 30
        
        # 左侧：角色列表
        frame:
            xsize 400
            ysize 600
            padding (20, 20)
            background "#2c3e50"
            
            vbox:
                spacing 15
                
                text "可用角色" size 24 color "#3498db"
                
                # 角色列表滚动区域
                viewport:
                    xsize 360
                    ysize 500
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 10
                        
                        if available_characters:
                            for character in available_characters:
                                textbutton "[character.name] ([character.age]岁, [character.gender.value])":
                                    action Return(character)
                                    text_size 14
                                    xsize 340
                                    ysize 40
                                    text_color "#ecf0f1"
                                    text_hover_color "#3498db"
                                    background "#34495e"
                                    hover_background "#2c3e50"
                        else:
                            text "没有可用角色" size 16 color "#95a5a6" xalign 0.5
                
                # 底部按钮
                hbox:
                    spacing 10
                    textbutton "刷新列表" action Function(refresh_character_list) text_size 14
                    textbutton "创建角色" action Jump("character_creation") text_size 14
        
        # 右侧：角色详情和操作
        frame:
            xsize 500
            ysize 600
            padding (20, 20)
            background "#2c3e50"
            
            vbox:
                spacing 15
                
                if selected_character:
                    # 角色详细信息
                    text "角色详情" size 24 color "#3498db"
                    
                    frame:
                        padding (15, 15)
                        background "#34495e"
                        
                        vbox:
                            spacing 10
                            
                            text "[selected_character.name]" size 20 color "#e74c3c"
                            
                            hbox:
                                spacing 30
                                vbox:
                                    spacing 5
                                    text "基本信息:" size 14 color "#f39c12"
                                    text "年龄: [selected_character.age]岁" size 12 color "#bdc3c7"
                                    text "性别: [selected_character.gender.value]" size 12 color "#bdc3c7"
                                    text "教育: [selected_character.education_level.value]" size 12 color "#bdc3c7"
                                
                                vbox:
                                    spacing 5
                                    text "经济状况:" size 14 color "#f39c12"
                                    text "现金: ¥[int(selected_character.cash)]" size 12 color "#bdc3c7"
                                    text "存款: ¥[int(selected_character.bank_balance)]" size 12 color "#bdc3c7"
                                    text "月收入: ¥[int(selected_character.monthly_income)]" size 12 color "#bdc3c7"
                    
                    # 状态条
                    frame:
                        padding (15, 15)
                        background "#34495e"
                        
                        vbox:
                            spacing 10
                            text "当前状态:" size 14 color "#f39c12"
                            
                            # 能量条
                            hbox:
                                spacing 10
                                text "能量:" size 12 color "#bdc3c7" xsize 60
                                bar value selected_character.daily_stats.energy range 100 xsize 200 thumb None
                                text "[int(selected_character.daily_stats.energy)]" size 12 color "#f39c12"
                            
                            # 幸福度条
                            hbox:
                                spacing 10
                                text "幸福:" size 12 color "#bdc3c7" xsize 60
                                bar value selected_character.daily_stats.happiness range 100 xsize 200 thumb None
                                text "[int(selected_character.daily_stats.happiness)]" size 12 color "#e67e22"
                            
                            # 健康条
                            hbox:
                                spacing 10
                                text "健康:" size 12 color "#bdc3c7" xsize 60
                                bar value selected_character.daily_stats.health range 100 xsize 200 thumb None
                                text "[int(selected_character.daily_stats.health)]" size 12 color "#27ae60"
                            
                            # 压力条
                            hbox:
                                spacing 10
                                text "压力:" size 12 color "#bdc3c7" xsize 60
                                bar value selected_character.daily_stats.stress range 100 xsize 200 thumb None
                                text "[int(selected_character.daily_stats.stress)]" size 12 color "#e74c3c"
                    
                    # 性格特征
                    frame:
                        padding (15, 15)
                        background "#34495e"
                        
                        vbox:
                            spacing 10
                            text "性格特征:" size 14 color "#f39c12"
                            
                            hbox:
                                spacing 20
                                vbox:
                                    spacing 3
                                    text "尽责性: [int(selected_character.personality.conscientiousness)]" size 11 color "#bdc3c7"
                                    text "外向性: [int(selected_character.personality.extraversion)]" size 11 color "#bdc3c7"
                                    text "开放性: [int(selected_character.personality.openness)]" size 11 color "#bdc3c7"
                                
                                vbox:
                                    spacing 3
                                    text "宜人性: [int(selected_character.personality.agreeableness)]" size 11 color "#bdc3c7"
                                    text "神经质: [int(selected_character.personality.neuroticism)]" size 11 color "#bdc3c7"
                    
                    null height 20
                    
                    # 操作按钮
                    hbox:
                        spacing 15
                        textbutton "扮演此角色" action Function(switch_to_character_mode, selected_character.id) text_size 16
                        textbutton "编辑角色" action Function(edit_character, selected_character.id) text_size 16
                
                else:
                    text "请选择一个角色查看详情" size 18 color "#95a5a6" xalign 0.5 yalign 0.5
    
    # 底部导航
    hbox:
        xalign 0.5
        ypos 680
        spacing 20
        
        textbutton "上帝模式" action Function(switch_to_god_mode) text_size 16
        textbutton "观察者模式" action Function(switch_to_observer_mode) text_size 16
        textbutton "返回" action Jump("world_browser") text_size 16

# 角色体验界面
screen character_experience_screen():
    tag menu
    
    # 背景
    add "#1a252f"
    
    # 当前角色信息栏
    frame:
        xalign 0.5
        ypos 10
        xsize 800
        padding (20, 10)
        background "#34495e"
        
        hbox:
            spacing 30
            
            # 角色基本信息
            vbox:
                spacing 5
                text "[current_character.name]的视角" size 24 color "#e74c3c"
                text "位置: [current_character.current_location]" size 14 color "#bdc3c7"
                text "时间: [current_time_info.get('time_string', '')]" size 14 color "#bdc3c7"
            
            # 快速状态
            hbox:
                spacing 20
                vbox:
                    spacing 2
                    text "能量" size 12 color "#f39c12"
                    bar value current_character.daily_stats.energy range 100 xsize 80 ysize 10 thumb None
                
                vbox:
                    spacing 2
                    text "幸福" size 12 color "#e67e22"
                    bar value current_character.daily_stats.happiness range 100 xsize 80 ysize 10 thumb None
                
                vbox:
                    spacing 2
                    text "健康" size 12 color "#27ae60"
                    bar value current_character.daily_stats.health range 100 xsize 80 ysize 10 thumb None
    
    # 主要游戏区域
    frame:
        xalign 0.5
        ypos 100
        xsize 900
        ysize 500
        padding (20, 20)
        background "#2c3e50"
        
        hbox:
            spacing 20
            
            # 左侧：环境描述和互动
            frame:
                xsize 500
                ysize 460
                padding (15, 15)
                background "#34495e"
                
                vbox:
                    spacing 15
                    
                    text "环境" size 18 color "#3498db"
                    
                    # 环境描述
                    frame:
                        padding (10, 10)
                        background "#2c3e50"
                        
                        viewport:
                            xsize 470
                            ysize 200
                            scrollbars "vertical"
                            mousewheel True
                            
                            text character_environment_description size 14 color "#ecf0f1"
                    
                    text "可用行动" size 16 color "#f39c12"
                    
                    # 行动选项
                    viewport:
                        xsize 470
                        ysize 180
                        scrollbars "vertical"
                        mousewheel True
                        
                        vbox:
                            spacing 5
                            
                            if available_actions:
                                for action in available_actions:
                                    textbutton "[action['name']]" action Function(perform_character_action, action['id']) text_size 14
                                    text "  [action['description']]" size 12 color "#95a5a6"
                            else:
                                text "暂无可用行动" size 14 color "#95a5a6"
            
            # 右侧：角色状态和思考
            frame:
                xsize 360
                ysize 460
                padding (15, 15)
                background "#34495e"
                
                vbox:
                    spacing 15
                    
                    text "内心独白" size 16 color "#e67e22"
                    
                    frame:
                        padding (10, 10)
                        background "#2c3e50"
                        
                        viewport:
                            xsize 330
                            ysize 150
                            scrollbars "vertical"
                            mousewheel True
                            
                            text character_thoughts size 12 color "#ecf0f1"
                    
                    text "最近活动" size 16 color "#f39c12"
                    
                    viewport:
                        xsize 330
                        ysize 200
                        scrollbars "vertical"
                        mousewheel True
                        
                        vbox:
                            spacing 5
                            
                            if recent_activities:
                                for activity in recent_activities:
                                    text "• [activity]" size 12 color "#bdc3c7"
                            else:
                                text "暂无活动记录" size 12 color "#95a5a6"
    
    # 底部控制栏
    hbox:
        xalign 0.5
        ypos 620
        spacing 20
        
        textbutton "休息" action Function(character_rest) text_size 14
        textbutton "工作" action Function(character_work) text_size 14
        textbutton "社交" action Function(character_socialize) text_size 14
        textbutton "购物" action Function(character_shop) text_size 14
        textbutton "查看状态" action Function(show_character_status) text_size 14
        textbutton "切换角色" action Jump("character_selection") text_size 14
        textbutton "退出角色模式" action Function(exit_character_mode) text_size 14

# 全局变量和函数
init python:
    available_characters = []
    selected_character = None
    current_character = None
    character_environment_description = ""
    character_thoughts = ""
    available_actions = []
    recent_activities = []
    current_time_info = {}
    player_system = None
    
    def init_player_system():
        global player_system, db_manager
        if not db_manager:
            return False
        
        try:
            from core.player_system import PlayerSystem
            from core.time_system import TimeSystem
            
            time_sys = TimeSystem(db_manager)
            player_system = PlayerSystem(db_manager)
            return True
        except Exception as e:
            print(f"玩家系统初始化失败: {e}")
            return False
    
    def refresh_character_list():
        global available_characters, player_system
        
        if not player_system:
            if not init_player_system():
                renpy.notify("系统初始化失败")
                return
        
        try:
            available_characters = player_system.get_available_characters(refresh=True)
            renpy.notify(f"已刷新，共{len(available_characters)}个角色")
        except Exception as e:
            renpy.notify(f"刷新失败: {e}")
    
    def switch_to_character_mode(character_id):
        global player_system, current_character
        
        if not player_system:
            if not init_player_system():
                renpy.notify("系统初始化失败")
                return
        
        try:
            success = player_system.switch_to_character_mode(character_id)
            if success:
                current_character = player_system.get_current_character()
                update_character_experience()
                renpy.jump("character_experience")
            else:
                renpy.notify("切换角色失败")
        except Exception as e:
            renpy.notify(f"切换失败: {e}")
    
    def switch_to_god_mode():
        global player_system
        
        if not player_system:
            if not init_player_system():
                renpy.notify("系统初始化失败")
                return
        
        try:
            success = player_system.switch_to_god_mode()
            if success:
                renpy.notify("已切换到上帝模式")
                renpy.jump("god_mode_editor")
            else:
                renpy.notify("切换失败")
        except Exception as e:
            renpy.notify(f"切换失败: {e}")
    
    def switch_to_observer_mode():
        global player_system
        
        if not player_system:
            if not init_player_system():
                renpy.notify("系统初始化失败")
                return
        
        try:
            success = player_system.switch_to_observer_mode()
            if success:
                renpy.notify("已切换到观察者模式")
                renpy.jump("world_browser")
            else:
                renpy.notify("切换失败")
        except Exception as e:
            renpy.notify(f"切换失败: {e}")
    
    def update_character_experience():
        global current_character, character_environment_description, character_thoughts, available_actions, recent_activities, current_time_info
        
        if not current_character:
            return
        
        try:
            from core.time_system import TimeSystem
            time_sys = TimeSystem(db_manager)
            current_time_info = time_sys.get_current_time_info()
            
            # 更新环境描述
            character_environment_description = f"你现在位于{current_character.current_location}。这里是一个繁忙的地区，周围有各种设施和其他居民。当前是{current_time_info.get('season', '春季')}，天气适宜。"
            
            # 更新内心独白
            energy_level = current_character.daily_stats.energy
            happiness_level = current_character.daily_stats.happiness
            
            if energy_level > 70:
                energy_thought = "我感觉精力充沛，可以做很多事情。"
            elif energy_level > 30:
                energy_thought = "我的精力还算可以，但需要注意休息。"
            else:
                energy_thought = "我感到有些疲惫，需要好好休息一下。"
            
            if happiness_level > 70:
                mood_thought = "今天心情不错，对生活充满期待。"
            elif happiness_level > 30:
                mood_thought = "心情还算平静，没什么特别的感觉。"
            else:
                mood_thought = "最近心情有些低落，希望能有所改善。"
            
            character_thoughts = f"{energy_thought}\n\n{mood_thought}\n\n我在想接下来应该做什么..."
            
            # 更新可用行动
            available_actions = [
                {"id": "rest", "name": "休息", "description": "恢复精力和健康"},
                {"id": "work", "name": "工作", "description": "赚取收入，但会消耗精力"},
                {"id": "socialize", "name": "社交", "description": "与他人互动，提升幸福感"},
                {"id": "exercise", "name": "锻炼", "description": "提升健康，但会消耗精力"},
                {"id": "shop", "name": "购物", "description": "购买物品，提升生活质量"},
                {"id": "learn", "name": "学习", "description": "提升技能和知识"}
            ]
            
            # 更新最近活动
            recent_activities = [
                f"在{current_character.current_location}度过了一段时间",
                "思考人生和未来的计划",
                "观察周围的环境和人群"
            ]
            
        except Exception as e:
            print(f"更新角色体验失败: {e}")
    
    def perform_character_action(action_id):
        global current_character, player_system
        
        if not current_character or not player_system:
            renpy.notify("系统错误")
            return
        
        try:
            # 记录玩家操作
            player_system.record_action(action_id, {"character_id": current_character.id})
            
            # 执行行动效果
            if action_id == "rest":
                current_character.daily_stats.energy = min(100, current_character.daily_stats.energy + 20)
                current_character.daily_stats.stress = max(0, current_character.daily_stats.stress - 10)
                renpy.notify("你休息了一会，感觉精力恢复了一些")
            
            elif action_id == "work":
                if current_character.daily_stats.energy > 20:
                    current_character.daily_stats.energy -= 15
                    current_character.cash += 50
                    renpy.notify("你工作了一段时间，赚了50元")
                else:
                    renpy.notify("你太累了，无法工作")
            
            elif action_id == "socialize":
                current_character.daily_stats.happiness = min(100, current_character.daily_stats.happiness + 15)
                current_character.daily_stats.energy -= 5
                renpy.notify("你与他人愉快地交流，心情变好了")
            
            elif action_id == "exercise":
                if current_character.daily_stats.energy > 15:
                    current_character.daily_stats.energy -= 10
                    current_character.daily_stats.health = min(100, current_character.daily_stats.health + 10)
                    renpy.notify("你锻炼了身体，感觉更健康了")
                else:
                    renpy.notify("你太累了，无法锻炼")
            
            elif action_id == "shop":
                if current_character.cash > 20:
                    current_character.cash -= 20
                    current_character.daily_stats.happiness = min(100, current_character.daily_stats.happiness + 10)
                    renpy.notify("你买了一些东西，花费了20元")
                else:
                    renpy.notify("你的钱不够购物")
            
            elif action_id == "learn":
                current_character.daily_stats.energy -= 10
                renpy.notify("你学习了新知识，感觉很充实")
            
            # 保存角色状态
            db_manager.save_npc(current_character)
            
            # 更新界面
            update_character_experience()
            
        except Exception as e:
            renpy.notify(f"执行行动失败: {e}")
    
    def exit_character_mode():
        global player_system
        
        if player_system:
            player_system.switch_to_god_mode()
        
        renpy.jump("world_browser")

# 角色选择入口
label character_selection:
    call init_system
    
    python:
        if not init_player_system():
            renpy.notify("玩家系统初始化失败")
            renpy.jump("world_browser")
        
        refresh_character_list()
    
    call screen character_selection_screen
    
    return

# 角色体验入口
label character_experience:
    call init_system
    
    python:
        if not current_character:
            renpy.jump("character_selection")
        
        update_character_experience()
    
    call screen character_experience_screen
    
    return

# -*- coding: utf-8 -*-
# 新闻系统界面

# 新闻主界面
screen news_system_screen():
    tag menu
    
    # 背景
    add "#1a252f"
    
    # 标题栏
    frame:
        xalign 0.5
        ypos 20
        padding (20, 10)
        background "#34495e"
        
        text "新闻中心" size 30 color "#ecf0f1" xalign 0.5
    
    # 主要内容区域
    hbox:
        xalign 0.5
        yalign 0.5
        spacing 30
        
        # 左侧：新闻分类导航
        frame:
            xsize 200
            ysize 600
            padding (15, 15)
            background "#2c3e50"
            
            vbox:
                spacing 10
                
                text "新闻分类" size 20 color "#ecf0f1"
                
                null height 10
                
                textbutton "全部新闻" action SetVariable("selected_news_category", "all") text_size 16
                textbutton "经济新闻" action SetVariable("selected_news_category", "经济") text_size 16
                textbutton "社会新闻" action SetVariable("selected_news_category", "社会") text_size 16
                textbutton "世界新闻" action SetVariable("selected_news_category", "世界") text_size 16
                textbutton "突发新闻" action SetVariable("selected_news_category", "突发") text_size 16
                textbutton "生活新闻" action SetVariable("selected_news_category", "生活") text_size 16
                
                null height 20
                
                text "时间筛选" size 18 color "#ecf0f1"
                
                textbutton "最近24小时" action SetVariable("news_time_filter", 24) text_size 14
                textbutton "最近3天" action SetVariable("news_time_filter", 72) text_size 14
                textbutton "最近一周" action SetVariable("news_time_filter", 168) text_size 14
                textbutton "全部时间" action SetVariable("news_time_filter", 0) text_size 14
        
        # 中间：新闻列表
        frame:
            xsize 500
            ysize 600
            padding (20, 20)
            background "#34495e"
            
            vbox:
                spacing 15
                
                # 分类标题
                if selected_news_category == "all":
                    text "全部新闻" size 24 color "#3498db"
                else:
                    text "[selected_news_category]新闻" size 24 color "#3498db"
                
                # 新闻列表滚动区域
                viewport:
                    xsize 460
                    ysize 520
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 10
                        
                        # 显示新闻列表
                        if current_news_list:
                            for news_item in current_news_list:
                                frame:
                                    padding (15, 10)
                                    background "#2c3e50"
                                    
                                    vbox:
                                        spacing 5
                                        
                                        # 新闻标题
                                        textbutton "[news_item.title]" action SetVariable("selected_news_item", news_item) text_size 16 text_color "#e74c3c"
                                        
                                        # 新闻元信息
                                        hbox:
                                            spacing 15
                                            text "类型: [news_item.event_type.value]" size 12 color "#95a5a6"
                                            text "优先级: [news_item.priority.name]" size 12 color "#95a5a6"
                                            text "时间: [news_item.created_at[:16]]" size 12 color "#95a5a6"
                                        
                                        # 新闻预览
                                        text "[news_item.content[:50]]..." size 14 color "#bdc3c7"
                        else:
                            text "暂无新闻" size 16 color "#95a5a6" xalign 0.5
        
        # 右侧：新闻详情
        frame:
            xsize 400
            ysize 600
            padding (20, 20)
            background "#34495e"
            
            vbox:
                spacing 15
                
                if selected_news_item:
                    # 新闻详情标题
                    text "新闻详情" size 20 color "#3498db"
                    
                    # 新闻标题
                    text "[selected_news_item.title]" size 18 color "#e74c3c"
                    
                    null height 10
                    
                    # 新闻元信息
                    frame:
                        padding (10, 10)
                        background "#2c3e50"
                        
                        vbox:
                            spacing 5
                            text "发布时间: [selected_news_item.created_at[:19]]" size 12 color "#95a5a6"
                            text "新闻类型: [selected_news_item.event_type.value]" size 12 color "#95a5a6"
                            text "优先级: [selected_news_item.priority.name]" size 12 color "#95a5a6"
                            text "AI提供商: [selected_news_item.ai_provider]" size 12 color "#95a5a6"
                    
                    null height 10
                    
                    # 新闻内容
                    frame:
                        padding (15, 15)
                        background "#2c3e50"
                        
                        viewport:
                            xsize 350
                            ysize 300
                            scrollbars "vertical"
                            mousewheel True
                            
                            text "[selected_news_item.content]" size 14 color "#ecf0f1"
                    
                    # 影响信息
                    if selected_news_item.affected_npcs or selected_news_item.world_impact or selected_news_item.economic_impact:
                        null height 10
                        
                        frame:
                            padding (10, 10)
                            background "#2c3e50"
                            
                            vbox:
                                spacing 5
                                text "事件影响:" size 14 color "#f39c12"
                                
                                if selected_news_item.affected_npcs:
                                    text "影响角色: [len(selected_news_item.affected_npcs)]人" size 12 color "#bdc3c7"
                                
                                if selected_news_item.world_impact:
                                    text "世界影响: 是" size 12 color "#bdc3c7"
                                
                                if selected_news_item.economic_impact:
                                    text "经济影响: 是" size 12 color "#bdc3c7"
                else:
                    text "选择一条新闻查看详情" size 16 color "#95a5a6" xalign 0.5 yalign 0.5
    
    # 底部按钮
    hbox:
        xalign 0.5
        ypos 680
        spacing 20
        
        textbutton "刷新新闻" action Function(refresh_news) text_size 16
        textbutton "生成新闻" action Function(generate_news_manually) text_size 16
        textbutton "返回" action Jump("world_browser") text_size 16

# 新闻摘要屏幕（可以在其他界面调用）
screen news_summary_widget():
    frame:
        xsize 300
        ysize 200
        padding (15, 15)
        background "#34495e"
        
        vbox:
            spacing 10
            
            text "新闻摘要" size 18 color "#3498db"
            
            if news_summary:
                text "总新闻数: [news_summary.get('total_articles', 0)]" size 14 color "#bdc3c7"
                text "最近24小时: [news_summary.get('recent_articles', 0)]条" size 14 color "#bdc3c7"
                text "突发新闻: [news_summary.get('breaking_news', 0)]条" size 14 color "#e74c3c"
                
                if news_summary.get('latest_article'):
                    null height 5
                    text "最新:" size 12 color "#f39c12"
                    text "[news_summary['latest_article'].title[:30]]..." size 12 color "#95a5a6"
            else:
                text "暂无新闻数据" size 14 color "#95a5a6"
            
            null height 10
            textbutton "查看全部" action Jump("news_system") text_size 12

# 全局变量和函数
init python:
    selected_news_category = "all"
    news_time_filter = 24
    selected_news_item = None
    current_news_list = []
    news_summary = {}
    news_system = None
    
    def init_news_system():
        global news_system, db_manager
        if not db_manager:
            return False
        
        try:
            from core.ai_event_generator import NewsSystem
            from core.ai_service import ai_service_manager
            
            news_system = NewsSystem(db_manager, ai_service_manager)
            return True
        except Exception as e:
            print(f"新闻系统初始化失败: {e}")
            return False
    
    def refresh_news():
        global current_news_list, news_summary, news_system
        
        if not news_system:
            if not init_news_system():
                renpy.notify("新闻系统初始化失败")
                return
        
        try:
            # 获取新闻摘要
            news_summary = news_system.get_news_summary()
            
            # 根据选择的分类和时间筛选获取新闻
            if selected_news_category == "all":
                if news_time_filter > 0:
                    current_news_list = news_system.get_recent_news(news_time_filter)
                else:
                    current_news_list = news_system.news_articles
            else:
                category_news = news_system.get_news_by_category(selected_news_category)
                if news_time_filter > 0:
                    # 按时间筛选
                    from datetime import datetime, timedelta
                    cutoff_time = datetime.now() - timedelta(hours=news_time_filter)
                    current_news_list = [
                        news for news in category_news
                        if datetime.fromisoformat(news.created_at) >= cutoff_time
                    ]
                else:
                    current_news_list = category_news
            
            # 按时间排序（最新的在前）
            current_news_list.sort(key=lambda x: x.created_at, reverse=True)
            
            renpy.notify(f"已刷新，共{len(current_news_list)}条新闻")
            
        except Exception as e:
            renpy.notify(f"刷新新闻失败: {e}")
    
    def generate_news_manually():
        global news_system
        
        if not news_system:
            if not init_news_system():
                renpy.notify("新闻系统初始化失败")
                return
        
        try:
            # 构建上下文
            from core.ai_event_generator import EventContext
            from core.time_system import TimeSystem
            
            time_sys = TimeSystem(db_manager)
            active_npcs = db_manager.load_all_active_npcs()
            
            context = EventContext(
                world_state={"total_population": len(active_npcs)},
                recent_events=[],
                active_npcs=active_npcs,
                economic_data=time_sys.economy_system.get_economic_summary(),
                time_info=time_sys.get_current_time_info(),
                market_events=list(time_sys.market_event_system.active_events.values())
            )
            
            # 生成新闻
            new_articles = news_system.generate_daily_news(context)
            
            if new_articles:
                renpy.notify(f"成功生成{len(new_articles)}条新闻")
                refresh_news()  # 刷新显示
            else:
                renpy.notify("暂时无法生成新闻")
                
        except Exception as e:
            renpy.notify(f"生成新闻失败: {e}")

# 新闻系统入口标签
label news_system:
    call init_system
    
    python:
        if not init_news_system():
            renpy.notify("新闻系统初始化失败")
            renpy.jump("world_browser")
        
        refresh_news()
    
    call screen news_system_screen
    
    return

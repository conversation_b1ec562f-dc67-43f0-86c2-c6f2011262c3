# -*- coding: utf-8 -*-
# 人类繁荣 - 主游戏脚本
# 基于最小化测试脚本扩展的完整游戏体验

# 声明角色
define narrator = Character(None, kind=nvl)
define player = Character("玩家", color="#3498db")
define system = Character("系统", color="#e74c3c")
define advisor = Character("顾问", color="#f39c12")

# 使用default声明变量，确保在store中正确初始化
default core_loaded = False
default current_world = None
default db_manager = None
default world_generator = None
default time_system = None
default economy_system = None
default npc_generator = None
default player_character = None
default init_success = False
default game_initialized = False
default current_location = None
default current_day_phase = "morning"  # morning, afternoon, evening, night
default selected_npc = None
default player_cash = 1000.0
default player_energy = 100
default player_happiness = 70
default player_stress = 30
default player_health = 90
default player_hunger = 20

# 初始化
init python:
    import sys
    import os
    import random
    from datetime import datetime

    global core_loaded, current_world, db_manager, world_generator, init_success
    global time_system, economy_system, npc_generator, player_character

    print("开始游戏初始化...")

    # 添加game目录到Python路径
    try:
        game_path = os.path.join(config.gamedir)
        if game_path not in sys.path:
            sys.path.insert(0, game_path)
        print(f"已添加路径: {game_path}")
    except Exception as e:
        print(f"路径添加失败: {e}")

    # 尝试导入核心模块
    try:
        print("尝试导入核心模块...")
        from core import DatabaseManager, WorldGenerator, NPCGenerator, TimeSystem, EconomySystem
        from core import World, Region, Block, NPC, Job, Skill, DailyStats
        from core.data_models import Gender, JobType, ActivityType

        # 设置全局变量
        core_loaded = True
        print("所有核心模块导入成功")

    except ImportError as e:
        print(f"导入错误: {e}")
        core_loaded = False
    except Exception as e:
        print(f"其他错误: {e}")
        core_loaded = False

    # 游戏功能函数
    def initialize_game():
        """初始化游戏系统"""
        global db_manager, world_generator, time_system, economy_system, npc_generator
        global current_world, init_success, game_initialized

        try:
            # 初始化数据库
            db_manager = DatabaseManager()

            # 初始化世界生成器
            world_generator = WorldGenerator(db_manager)

            # 检查现有世界
            existing_worlds = db_manager.list_worlds()
            if existing_worlds:
                current_world = db_manager.load_world(existing_worlds[0]['id'])
            else:
                current_world = None

            # 初始化其他系统
            time_system = TimeSystem(db_manager)
            economy_system = EconomySystem(db_manager, time_system)
            npc_generator = NPCGenerator(db_manager)

            init_success = True
            game_initialized = True
            return True

        except Exception as e:
            print(f"游戏初始化失败: {e}")
            import traceback
            traceback.print_exc()
            init_success = False
            return False

    def create_new_world(world_name="新世界"):
        """创建新的游戏世界"""
        global current_world, world_generator

        try:
            current_world = world_generator.generate_world(world_name)
            return True
        except Exception as e:
            print(f"世界创建失败: {e}")
            return False

    def advance_time(hours=1):
        """推进游戏时间"""
        global time_system, current_day_phase

        if not time_system:
            return False

        try:
            # 推进时间
            results = time_system.advance_time(hours)

            # 更新日间阶段
            current_time = time_system.get_current_time_info()
            hour = current_time.get("hour", 8)

            if 5 <= hour < 12:
                current_day_phase = "morning"
            elif 12 <= hour < 18:
                current_day_phase = "afternoon"
            elif 18 <= hour < 22:
                current_day_phase = "evening"
            else:
                current_day_phase = "night"

            return results
        except Exception as e:
            print(f"时间推进失败: {e}")
            return False

    def get_random_npcs(count=5):
        """获取随机NPC列表"""
        global db_manager

        if not db_manager:
            return []

        try:
            all_npcs = db_manager.load_all_active_npcs()
            if len(all_npcs) <= count:
                return all_npcs
            return random.sample(all_npcs, count)
        except Exception as e:
            print(f"获取NPC失败: {e}")
            return []

    def update_player_stats(energy_change=0, happiness_change=0, stress_change=0,
                            health_change=0, hunger_change=0, cash_change=0):
        """更新玩家状态"""
        global player_energy, player_happiness, player_stress, player_health, player_hunger, player_cash

        player_energy = max(0, min(100, player_energy + energy_change))
        player_happiness = max(0, min(100, player_happiness + happiness_change))
        player_stress = max(0, min(100, player_stress + stress_change))
        player_health = max(0, min(100, player_health + health_change))
        player_hunger = max(0, min(100, player_hunger + hunger_change))
        player_cash += cash_change

        return True

# 游戏开始
label start:
    # 初始化游戏系统
    $ initialize_game()

    if init_success:
        jump game_main_menu
    else:
        "系统初始化失败，请查看控制台输出。"
        return

# 主菜单
label game_main_menu:
    scene bg black
    with fade

    # 播放主菜单音乐（如果有）
    # $ renpy.music.play("audio/main_theme.mp3", loop=True, if_changed=True)

    # 显示主菜单界面
    show screen game_main_menu_screen

    # 等待用户选择
    $ result = ui.interact()

    # 处理用户选择
    if result == "new_game":
        hide screen game_main_menu_screen
        # 停止主菜单音乐
        # $ renpy.music.stop()
        jump new_game_setup

    elif result == "continue":
        hide screen game_main_menu_screen
        # 停止主菜单音乐
        # $ renpy.music.stop()
        jump continue_game

    elif result == "browse_world":
        hide screen game_main_menu_screen

        if current_world:
            # 如果已有世界，直接浏览
            jump world_overview
        else:
            # 如果没有世界，提示创建
            "没有可浏览的世界。请先创建新游戏。"
            jump game_main_menu

    elif result == "settings":
        hide screen game_main_menu_screen
        jump settings_menu

    elif result == "exit":
        # 显示确认退出对话框
        $ renpy.call_screen("confirm", message="确定要退出游戏吗？", yes_action=Return(True), no_action=Return(False))

        # 如果用户确认退出
        if _return:
            return
        else:
            jump game_main_menu

    # 如果没有匹配的选项，返回主菜单
    jump game_main_menu

# 新游戏设置
label new_game_setup:
    scene bg black
    with fade

    "欢迎来到《人类繁荣》！"
    "这是一个模拟人类社会发展的游戏，你将见证并参与一个小型社会的成长。"

    # 创建新世界
    if current_world is None:
        "正在创建新的游戏世界..."
        $ success = create_new_world("新世界")
        if not success:
            "世界创建失败，请重试。"
            jump game_main_menu

    "世界创建成功！"
    "现在，让我们开始你的旅程..."

    jump game_intro

# 游戏介绍
label game_intro:
    scene bg black
    with fade

    advisor "欢迎来到新世界，我是你的顾问。"
    advisor "在这个世界中，你将观察并影响一个小型社会的发展。"
    advisor "你可以与NPC互动，参与经济活动，影响社会发展。"
    advisor "让我们开始吧！"

    jump world_overview

# 世界概览
label world_overview:
    scene bg black
    with fade

    call screen world_browser_screen

    jump daily_routine

# 日常活动循环
label daily_routine:
    scene bg black
    with fade

    # 显示游戏状态HUD
    show screen game_status_hud
    show screen quick_menu

    # 获取当前时间信息
    $ current_time_info = time_system.get_current_time_info() if time_system else {"time_string": "未知时间"}
    $ time_str = current_time_info.get("time_string", "未知时间")
    $ current_hour = current_time_info.get("hour", 8) if time_system else 8

    # 根据时间段显示不同的背景描述
    if current_day_phase == "morning":
        "清晨的阳光洒向大地，新的一天开始了。"
    elif current_day_phase == "afternoon":
        "午后的阳光温暖而明亮，人们忙碌着各自的事务。"
    elif current_day_phase == "evening":
        "夕阳西下，一天的工作即将结束。"
    else:
        "夜幕降临，城市灯火通明，夜生活开始了。"

    "现在是 [time_str]。"

    # 检查玩家状态并给出提示
    python:
        status_warnings = []
        if player_energy < 20:
            status_warnings.append("你感到非常疲惫")
        if player_hunger > 80:
            status_warnings.append("你感到很饿")
        if player_stress > 80:
            status_warnings.append("你感到压力很大")
        if player_health < 30:
            status_warnings.append("你的健康状况不佳")

        if status_warnings:
            warning_text = "注意：" + "，".join(status_warnings) + "。"
        else:
            warning_text = ""

    if warning_text:
        "[warning_text]"

    # 随机事件检查
    python:
        import random
        random_event_chance = random.randint(1, 100)

        # 10%概率触发随机事件
        if random_event_chance <= 10:
            random_event_triggered = True
        else:
            random_event_triggered = False

    if random_event_triggered:
        jump random_event

    # 主要活动菜单
    menu:
        "你想做什么？"

        "浏览世界" if current_world:
            hide screen game_status_hud
            hide screen quick_menu
            jump world_overview

        "与NPC互动":
            hide screen game_status_hud
            hide screen quick_menu
            jump npc_interaction

        "角色体验模式":
            hide screen game_status_hud
            hide screen quick_menu
            jump character_mode_entry

        "经济活动":
            hide screen game_status_hud
            hide screen quick_menu
            jump economy_activities

        "个人活动":
            hide screen game_status_hud
            hide screen quick_menu
            jump personal_activities

        "探索城市":
            hide screen game_status_hud
            hide screen quick_menu
            jump explore_city

        "查看新闻":
            hide screen game_status_hud
            hide screen quick_menu
            jump check_news

        "查看状态":
            hide screen game_status_hud
            hide screen quick_menu
            jump check_status

        "保存游戏":
            "游戏已保存。"
            jump daily_routine

        "返回主菜单":
            hide screen game_status_hud
            hide screen quick_menu
            jump game_main_menu

# 随机事件
label random_event:
    python:
        import random
        events = [
            {
                "title": "意外收获",
                "description": "你在路上捡到了一些钱！",
                "effects": {"cash_change": 50, "happiness_change": 10}
            },
            {
                "title": "友善的陌生人",
                "description": "一个陌生人和你分享了有趣的故事。",
                "effects": {"happiness_change": 15, "stress_change": -5}
            },
            {
                "title": "小小挫折",
                "description": "你遇到了一些小麻烦，但很快解决了。",
                "effects": {"stress_change": 10, "energy_change": -5}
            },
            {
                "title": "美好天气",
                "description": "今天的天气特别好，让你心情愉悦。",
                "effects": {"happiness_change": 20, "health_change": 5}
            },
            {
                "title": "意外支出",
                "description": "你需要支付一些意外的费用。",
                "effects": {"cash_change": -30, "stress_change": 5}
            }
        ]

        selected_event = random.choice(events)
        event_title = selected_event["title"]
        event_description = selected_event["description"]
        event_effects = selected_event["effects"]

    "突发事件：[event_title]"
    "[event_description]"

    $ update_player_stats(**event_effects)

    "按任意键继续..."
    pause

    jump daily_routine

# 个人活动
label personal_activities:
    scene bg black
    with fade

    menu:
        "选择个人活动："

        "休息一下":
            $ update_player_stats(energy_change=20, stress_change=-10, hunger_change=5)
            "你休息了一会儿，恢复了一些精力。"
            $ advance_time(1)

        "吃饭":
            if player_cash >= 20:
                $ update_player_stats(hunger_change=-30, health_change=5, cash_change=-20)
                "你吃了一顿美味的饭菜，饥饿感降低了。"
            else:
                "你没有足够的钱吃饭。"
            $ advance_time(1)

        "锻炼身体":
            $ update_player_stats(health_change=10, energy_change=-15, stress_change=-10, hunger_change=10)
            "你进行了一些锻炼，身体更健康了。"
            $ advance_time(2)

        "冥想放松":
            $ update_player_stats(stress_change=-20, happiness_change=10, energy_change=-5)
            "你进行了冥想，感到内心平静。"
            $ advance_time(1)

        "睡觉":
            $ update_player_stats(energy_change=50, stress_change=-20, health_change=10, hunger_change=15)
            "你睡了一觉，精力充沛地醒来。"
            $ advance_time(8)

        "学习技能":
            $ update_player_stats(energy_change=-10, stress_change=5, happiness_change=5)
            "你学习了一些新技能，感到很有成就感。"
            $ advance_time(3)

    jump daily_routine

# 探索城市
label explore_city:
    scene bg black
    with fade

    $ update_player_stats(energy_change=-10, happiness_change=15, hunger_change=5)

    python:
        import random
        exploration_results = [
            "你发现了一个美丽的公园，在那里度过了愉快的时光。",
            "你参观了一个有趣的博物馆，学到了很多新知识。",
            "你在街上遇到了一些友善的人，和他们聊了聊天。",
            "你找到了一家新开的咖啡店，品尝了美味的咖啡。",
            "你在城市中漫步，欣赏了美丽的建筑。",
            "你参加了一个小型的街头表演，感到很有趣。"
        ]

        result = random.choice(exploration_results)

    "你在城市中探索..."
    "[result]"

    $ advance_time(2)

    jump daily_routine

# 查看新闻
label check_news:
    scene bg black
    with fade

    "今日新闻："

    python:
        import random
        news_items = [
            "本地经济持续增长，就业率上升。",
            "新的公园建设项目获得批准。",
            "科技公司宣布在本地设立新的研发中心。",
            "本地学校获得教育质量奖项。",
            "环保项目取得显著成效。",
            "新的交通线路即将开通。",
            "本地艺术节即将举办。",
            "社区志愿者活动得到广泛支持。"
        ]

        selected_news = random.sample(news_items, 3)

    for news in selected_news:
        "• [news]"

    $ update_player_stats(happiness_change=5)
    $ advance_time(0.5)

    jump daily_routine

# 角色体验模式入口
label character_mode_entry:
    scene bg black
    with fade

    "进入角色体验模式..."
    "在这个模式中，你可以深入了解和体验不同NPC的生活。"

    # 获取可用角色
    $ available_characters = get_random_npcs(10)

    if not available_characters:
        "当前没有可体验的角色。"
        jump daily_routine

    # 显示角色选择界面
    call screen character_selection_screen

    # 处理选择结果
    if _return:
        $ selected_character = _return
        jump character_experience
    else:
        jump daily_routine

# 角色体验
label character_experience:
    scene bg black
    with fade

    "你现在以[selected_character.name]的视角体验生活。"

    # 显示角色详细信息
    python:
        char_name = selected_character.name
        char_age = selected_character.age
        char_gender = selected_character.gender.value
        char_cash = selected_character.cash if hasattr(selected_character, 'cash') else 0
        char_location = selected_character.current_location if hasattr(selected_character, 'current_location') else "未知"

        # 角色状态
        char_energy = selected_character.daily_stats.energy if hasattr(selected_character.daily_stats, 'energy') else 50
        char_happiness = selected_character.daily_stats.happiness if hasattr(selected_character.daily_stats, 'happiness') else 50
        char_stress = selected_character.daily_stats.stress if hasattr(selected_character.daily_stats, 'stress') else 30
        char_health = selected_character.daily_stats.health if hasattr(selected_character.daily_stats, 'health') else 70
        char_hunger = selected_character.daily_stats.hunger if hasattr(selected_character.daily_stats, 'hunger') else 30

        # 职业信息
        if hasattr(selected_character, 'current_job') and selected_character.current_job:
            char_job = selected_character.current_job.job_type.value
            char_salary = selected_character.current_job.salary if hasattr(selected_character.current_job, 'salary') else 0
        else:
            char_job = "无业"
            char_salary = 0

    "角色信息："
    "姓名：[char_name]"
    "年龄：[char_age]岁"
    "性别：[char_gender]"
    "职业：[char_job]"
    "现金：¥[char_cash:.2f]"
    "位置：[char_location]"

    "当前状态："
    "精力：[char_energy]/100"
    "心情：[char_happiness]/100"
    "压力：[char_stress]/100"
    "健康：[char_health]/100"
    "饥饿：[char_hunger]/100"

    menu:
        "选择体验内容："

        "体验日常生活":
            jump character_daily_life

        "查看人际关系":
            jump character_relationships

        "体验工作生活":
            jump character_work_life

        "查看个人目标":
            jump character_goals

        "模拟决策":
            jump character_decisions

        "返回角色选择":
            jump character_mode_entry

        "退出角色模式":
            jump daily_routine

# 角色日常生活体验
label character_daily_life:
    scene bg black
    with fade

    "你正在体验[selected_character.name]的日常生活..."

    python:
        import random

        # 根据时间段生成不同的日常活动
        if current_day_phase == "morning":
            activities = [
                "起床洗漱，准备开始新的一天",
                "享用早餐，查看今天的计划",
                "阅读新闻，了解世界动态",
                "进行晨练，保持身体健康",
                "整理房间，营造舒适环境"
            ]
        elif current_day_phase == "afternoon":
            activities = [
                "处理工作事务，专注于任务",
                "与同事或朋友交流",
                "享用午餐，稍作休息",
                "学习新技能，提升自己",
                "处理个人事务"
            ]
        elif current_day_phase == "evening":
            activities = [
                "结束一天的工作，准备回家",
                "与家人或朋友共度时光",
                "享用晚餐，放松身心",
                "进行娱乐活动，缓解压力",
                "规划明天的安排"
            ]
        else:
            activities = [
                "准备就寝，回顾今天",
                "阅读书籍，充实内心",
                "与朋友聊天，分享心情",
                "进行夜间散步，思考人生",
                "整理思绪，准备休息"
            ]

        selected_activity = random.choice(activities)

        # 根据角色性格和状态调整活动描述
        if char_happiness > 70:
            mood_modifier = "心情愉悦地"
        elif char_happiness < 30:
            mood_modifier = "有些沮丧地"
        else:
            mood_modifier = "平静地"

        if char_energy > 70:
            energy_modifier = "精力充沛地"
        elif char_energy < 30:
            energy_modifier = "疲惫地"
        else:
            energy_modifier = "正常地"

    "[selected_character.name][mood_modifier][energy_modifier][selected_activity]。"

    # 显示内心独白
    python:
        import random
        thoughts = [
            "今天过得还不错，希望明天会更好。",
            "生活虽然平凡，但也有它的美好。",
            "我需要更努力地工作，实现自己的目标。",
            "和朋友们在一起的时光总是很珍贵。",
            "这座城市的生活节奏有时让人感到压力。",
            "我想要改变现在的生活状态。",
            "家人的支持是我前进的动力。",
            "未来充满了可能性，我要抓住机会。"
        ]

        # 根据角色状态选择合适的想法
        if char_stress > 70:
            stress_thoughts = [
                "最近压力太大了，需要找时间放松。",
                "工作和生活的平衡真的很难把握。",
                "我需要学会更好地管理自己的情绪。"
            ]
            thoughts.extend(stress_thoughts)

        if char_happiness > 80:
            happy_thoughts = [
                "生活真美好，我感到很幸福。",
                "今天发生的事情让我很开心。",
                "我对未来充满期待。"
            ]
            thoughts.extend(happy_thoughts)

        selected_thought = random.choice(thoughts)

    "[selected_character.name]的内心独白："
    "「[selected_thought]」"

    menu:
        "继续体验："

        "观察更多日常细节":
            python:
                import random
                details = [
                    f"{selected_character.name}仔细整理着自己的物品，显示出对生活的认真态度。",
                    f"{selected_character.name}在镜子前整理仪容，准备面对新的挑战。",
                    f"{selected_character.name}查看手机消息，与朋友保持联系。",
                    f"{selected_character.name}望向窗外，思考着生活的意义。",
                    f"{selected_character.name}翻阅着日记，记录生活的点点滴滴。"
                ]
                detail = random.choice(details)

            "[detail]"

        "了解角色习惯":
            python:
                import random
                habits = [
                    f"{selected_character.name}有每天早起的习惯，认为这能带来好运。",
                    f"{selected_character.name}喜欢在睡前阅读，这让心灵得到平静。",
                    f"{selected_character.name}习惯记录每天的开支，是个理财高手。",
                    f"{selected_character.name}每周都会给家人打电话，维系亲情。",
                    f"{selected_character.name}喜欢收集有趣的小物件，这是个人爱好。"
                ]
                habit = random.choice(habits)

            "[habit]"

        "返回角色体验":
            jump character_experience

    jump character_experience

# 角色人际关系
label character_relationships:
    scene bg black
    with fade

    "查看[selected_character.name]的人际关系网络..."

    python:
        # 获取其他NPC作为关系网络
        all_npcs = get_random_npcs(15)
        other_npcs = [npc for npc in all_npcs if npc != selected_character]

        # 随机生成关系
        import random
        relationships = []

        relationship_types = [
            ("朋友", "经常一起聊天，关系很好"),
            ("同事", "工作上有合作，相处融洽"),
            ("邻居", "住得很近，偶尔打招呼"),
            ("熟人", "见过几次面，但不太熟"),
            ("好友", "关系非常亲密，无话不谈"),
            ("合作伙伴", "在某些项目上有合作"),
            ("导师", "在某些方面给予指导"),
            ("学生", "向其学习某些技能")
        ]

        # 为角色生成3-6个关系
        num_relationships = random.randint(3, min(6, len(other_npcs)))
        selected_npcs = random.sample(other_npcs, num_relationships)

        for npc in selected_npcs:
            rel_type, rel_desc = random.choice(relationship_types)
            relationships.append({
                "npc": npc,
                "type": rel_type,
                "description": rel_desc,
                "closeness": random.randint(20, 90)
            })

    "人际关系网络："

    for rel in relationships:
        $ rel_npc = rel["npc"]
        $ rel_type = rel["type"]
        $ rel_desc = rel["description"]
        $ closeness = rel["closeness"]

        "• [rel_npc.name] ([rel_type]) - 亲密度：[closeness]/100"
        "  [rel_desc]"

    menu:
        "选择操作："

        "深入了解某个关系":
            python:
                rel_choices = []
                for i, rel in enumerate(relationships):
                    rel_choices.append((f"了解与{rel['npc'].name}的关系", i))

            $ selected_rel_index = renpy.display_menu(rel_choices)
            $ selected_rel = relationships[selected_rel_index]

            "深入了解与[selected_rel['npc'].name]的关系..."

            python:
                import random
                rel_stories = [
                    f"你们是在{random.choice(['工作中', '社区活动中', '朋友介绍下', '偶然机会下'])}认识的。",
                    f"最近一次交流是{random.choice(['上周', '几天前', '昨天', '今天早上'])}，聊了{random.choice(['工作话题', '生活琐事', '共同兴趣', '未来计划'])}。",
                    f"{selected_rel['npc'].name}是个{random.choice(['很有趣', '很可靠', '很聪明', '很善良', '很幽默'])}的人。",
                    f"你们经常{random.choice(['一起吃饭', '互相帮助', '分享心情', '讨论问题', '参加活动'])}。"
                ]
                story = random.choice(rel_stories)

            "[story]"

        "模拟社交互动":
            python:
                import random
                selected_rel = random.choice(relationships)

                interaction_types = [
                    "发送消息问候",
                    "邀请共进午餐",
                    "分享有趣的事情",
                    "寻求建议帮助",
                    "约定见面时间"
                ]

                interaction = random.choice(interaction_types)

                # 根据关系亲密度决定互动结果
                if selected_rel["closeness"] > 70:
                    result = "互动很成功，关系进一步加深了。"
                elif selected_rel["closeness"] > 40:
                    result = "互动还不错，保持了良好的关系。"
                else:
                    result = "互动有些尴尬，但还是有所收获。"

            "你选择与[selected_rel['npc'].name][interaction]..."
            "[result]"

        "返回角色体验":
            jump character_experience

    jump character_relationships

# 角色工作生活
label character_work_life:
    scene bg black
    with fade

    if char_job == "无业":
        "目前[selected_character.name]没有工作，正在寻找合适的机会。"

        python:
            import random
            job_search_activities = [
                "浏览招聘网站，寻找合适的职位",
                "更新简历，突出个人优势",
                "联系朋友，了解工作机会",
                "参加招聘会，面对面交流",
                "学习新技能，提升竞争力"
            ]
            activity = random.choice(job_search_activities)

        "今天[selected_character.name]在[activity]。"

        menu:
            "体验求职过程："

            "模拟面试":
                python:
                    import random
                    interview_results = [
                        "面试表现不错，有希望获得这个职位。",
                        "面试过程中有些紧张，但整体还算顺利。",
                        "面试官对经验要求较高，需要继续努力。",
                        "面试氛围很好，双方都很满意。"
                    ]
                    result = random.choice(interview_results)

                "参加了一场面试..."
                "[result]"

            "技能学习":
                "花时间学习新技能，为未来的工作做准备。"
                "这种投资自己的行为让人感到充实。"

            "返回角色体验":
                jump character_experience
    else:
        "体验[selected_character.name]的工作生活..."
        "职业：[char_job]"

        python:
            import random

            # 根据不同职业生成不同的工作体验
            work_experiences = {
                "教师": [
                    "准备今天的课程内容，确保学生能够理解",
                    "批改学生作业，给出建设性的反馈",
                    "与同事讨论教学方法，分享经验",
                    "参加教师会议，了解学校最新政策"
                ],
                "医生": [
                    "仔细诊断患者病情，制定治疗方案",
                    "与患者沟通，解释治疗过程",
                    "参加医学研讨会，学习新的治疗方法",
                    "处理紧急情况，展现专业能力"
                ],
                "工程师": [
                    "设计新的技术方案，解决复杂问题",
                    "与团队成员协作，推进项目进度",
                    "测试产品功能，确保质量标准",
                    "学习新技术，保持竞争优势"
                ],
                "销售员": [
                    "拜访客户，介绍产品优势",
                    "处理客户投诉，维护客户关系",
                    "参加销售培训，提升销售技巧",
                    "分析市场趋势，制定销售策略"
                ]
            }

            # 默认工作体验
            default_experiences = [
                "专注地处理工作任务，追求效率和质量",
                "与同事交流合作，营造良好的工作氛围",
                "参加工作会议，了解公司最新动态",
                "学习新的工作技能，提升个人能力",
                "处理日常事务，保持工作的有序进行"
            ]

            if char_job in work_experiences:
                experiences = work_experiences[char_job]
            else:
                experiences = default_experiences

            selected_experience = random.choice(experiences)

            # 工作压力和满意度
            work_stress = random.randint(20, 80)
            work_satisfaction = random.randint(30, 90)

        "今天的工作内容："
        "[selected_experience]"

        "工作状态："
        "工作压力：[work_stress]/100"
        "工作满意度：[work_satisfaction]/100"

        menu:
            "深入体验工作："

            "体验工作挑战":
                python:
                    import random
                    challenges = [
                        "遇到了技术难题，需要创新思维来解决",
                        "面临紧急任务，需要在有限时间内完成",
                        "处理复杂的人际关系，需要沟通技巧",
                        "学习新的工作流程，适应变化",
                        "平衡多个项目，合理分配时间和精力"
                    ]
                    challenge = random.choice(challenges)

                    solutions = [
                        "通过团队合作成功解决了问题",
                        "运用专业知识找到了解决方案",
                        "寻求同事帮助，共同克服困难",
                        "保持冷静思考，逐步分析问题",
                        "学习新方法，提升解决问题的能力"
                    ]
                    solution = random.choice(solutions)

                "工作挑战：[challenge]"
                "解决方式：[solution]"

            "体验工作成就":
                python:
                    import random
                    achievements = [
                        "成功完成了重要项目，获得了上级认可",
                        "帮助同事解决问题，增进了团队关系",
                        "提出了创新建议，被公司采纳",
                        "超额完成了工作目标，感到很有成就感",
                        "获得了客户的好评，提升了个人声誉"
                    ]
                    achievement = random.choice(achievements)

                "工作成就：[achievement]"
                "这种成功的感觉让[selected_character.name]感到很满足。"

            "返回角色体验":
                jump character_experience

    jump character_work_life

# 角色个人目标
label character_goals:
    scene bg black
    with fade

    "查看[selected_character.name]的个人目标和梦想..."

    python:
        import random

        # 根据角色年龄和职业生成不同的目标
        short_term_goals = [
            "学习一项新技能",
            "改善健康状况",
            "存够一笔钱",
            "结交新朋友",
            "提升工作表现",
            "整理生活环境",
            "培养新爱好",
            "改善人际关系"
        ]

        long_term_goals = [
            "买一套属于自己的房子",
            "创办自己的事业",
            "环游世界",
            "找到人生伴侣",
            "在专业领域取得成就",
            "为社会做出贡献",
            "实现财务自由",
            "建立幸福的家庭"
        ]

        life_dreams = [
            "成为所在领域的专家",
            "写一本书分享人生感悟",
            "帮助更多需要帮助的人",
            "过上简单而充实的生活",
            "在历史上留下自己的印记",
            "培养下一代成才",
            "探索未知的领域",
            "实现内心的平静与满足"
        ]

        # 为角色选择目标
        char_short_goal = random.choice(short_term_goals)
        char_long_goal = random.choice(long_term_goals)
        char_dream = random.choice(life_dreams)

        # 目标进度
        short_progress = random.randint(10, 80)
        long_progress = random.randint(5, 40)

        # 根据角色状态调整目标描述
        if char_happiness > 70:
            motivation_level = "非常积极"
        elif char_happiness > 40:
            motivation_level = "比较积极"
        else:
            motivation_level = "有些消极"

    "个人目标规划："

    "短期目标：[char_short_goal]"
    "进度：[short_progress]%"

    "长期目标：[char_long_goal]"
    "进度：[long_progress]%"

    "人生梦想：[char_dream]"

    "当前动机水平：[motivation_level]"

    menu:
        "深入了解目标："

        "了解目标制定原因":
            python:
                import random
                reasons = [
                    f"这个目标对{selected_character.name}来说很重要，因为它代表着个人成长。",
                    f"{selected_character.name}希望通过实现这个目标来改善生活质量。",
                    f"这是{selected_character.name}从小就有的梦想，一直在努力追求。",
                    f"受到身边人的启发，{selected_character.name}决定设定这个目标。",
                    f"这个目标能够帮助{selected_character.name}获得更多的安全感。"
                ]
                reason = random.choice(reasons)

            "[reason]"

        "查看实现计划":
            python:
                import random
                plans = [
                    "制定了详细的时间表，每天都有具体的行动计划。",
                    "寻找相关的学习资源，不断提升自己的能力。",
                    "与有经验的人交流，获取宝贵的建议和指导。",
                    "设定阶段性的小目标，逐步向大目标迈进。",
                    "保持积极的心态，即使遇到困难也不轻易放弃。"
                ]
                plan = random.choice(plans)

            "实现计划：[plan]"

        "模拟目标追求过程":
            python:
                import random

                # 模拟一次目标追求的尝试
                attempt_success = random.randint(1, 100)

                if attempt_success <= 30:
                    result = "这次尝试遇到了一些困难，但学到了宝贵的经验。"
                    progress_change = random.randint(1, 5)
                elif attempt_success <= 70:
                    result = "取得了一些进展，离目标又近了一步。"
                    progress_change = random.randint(5, 15)
                else:
                    result = "这次尝试非常成功，大大推进了目标的实现。"
                    progress_change = random.randint(15, 25)

                # 更新进度（仅用于显示）
                new_progress = min(100, short_progress + progress_change)

            "尝试推进短期目标..."
            "[result]"
            "目标进度从[short_progress]%提升到[new_progress]%。"

        "返回角色体验":
            jump character_experience

    jump character_goals

# 角色决策模拟
label character_decisions:
    scene bg black
    with fade

    "模拟[selected_character.name]面临的人生决策..."

    python:
        import random

        # 根据角色状态生成不同类型的决策
        decision_scenarios = []

        # 工作相关决策
        if char_job == "无业":
            decision_scenarios.extend([
                {
                    "title": "工作机会选择",
                    "description": "收到了两个工作offer，一个薪资高但压力大，一个薪资一般但工作稳定。",
                    "options": [
                        ("选择高薪工作", {"cash_change": 500, "stress_change": 20, "happiness_change": 10}),
                        ("选择稳定工作", {"cash_change": 200, "stress_change": -5, "happiness_change": 15}),
                        ("继续寻找", {"energy_change": -10, "stress_change": 5})
                    ]
                }
            ])
        else:
            decision_scenarios.extend([
                {
                    "title": "职业发展",
                    "description": "有机会跳槽到更好的公司，但需要离开熟悉的环境。",
                    "options": [
                        ("接受新机会", {"cash_change": 300, "stress_change": 15, "happiness_change": 20}),
                        ("留在现公司", {"stress_change": -5, "happiness_change": 5}),
                        ("再考虑一下", {"stress_change": 10})
                    ]
                }
            ])

        # 生活相关决策
        decision_scenarios.extend([
            {
                "title": "居住选择",
                "description": "考虑搬到更好的住所，但租金会增加不少。",
                "options": [
                    ("搬到新住所", {"cash_change": -200, "happiness_change": 25, "health_change": 10}),
                    ("继续现在的住所", {"cash_change": 50, "happiness_change": -5}),
                    ("寻找其他选择", {"energy_change": -5})
                ]
            },
            {
                "title": "人际关系",
                "description": "朋友邀请参加一个重要的社交活动，但需要花费不少时间和金钱。",
                "options": [
                    ("参加活动", {"cash_change": -100, "happiness_change": 20, "energy_change": -10}),
                    ("礼貌拒绝", {"happiness_change": -5, "stress_change": 5}),
                    ("提出替代方案", {"happiness_change": 10})
                ]
            },
            {
                "title": "健康投资",
                "description": "考虑是否要花钱办健身卡，改善身体状况。",
                "options": [
                    ("办理健身卡", {"cash_change": -150, "health_change": 20, "happiness_change": 15}),
                    ("自己在家锻炼", {"health_change": 10, "energy_change": -5}),
                    ("暂时不考虑", {"health_change": -5})
                ]
            }
        ])

        # 随机选择一个决策场景
        selected_scenario = random.choice(decision_scenarios)
        scenario_title = selected_scenario["title"]
        scenario_desc = selected_scenario["description"]
        scenario_options = selected_scenario["options"]

    "决策场景：[scenario_title]"
    "[scenario_desc]"

    python:
        # 创建选择菜单
        choice_menu = []
        for i, (option_text, effects) in enumerate(scenario_options):
            choice_menu.append((option_text, i))

    $ selected_choice = renpy.display_menu(choice_menu)
    $ choice_text, choice_effects = scenario_options[selected_choice]

    "[selected_character.name]选择了：[choice_text]"

    python:
        # 分析决策结果
        import random

        decision_outcomes = [
            "这个决策体现了理性思考的重要性。",
            "每个选择都有其利弊，关键是要承担后果。",
            "这种决策能力是生活中的重要技能。",
            "通过这次决策，角色的人生轨迹可能会发生改变。",
            "决策的智慧往往来自于生活经验的积累。"
        ]

        outcome_analysis = random.choice(decision_outcomes)

        # 根据选择显示角色的内心想法
        if "cash_change" in choice_effects and choice_effects["cash_change"] > 0:
            thought = "这个选择虽然有经济收益，但也要考虑其他方面的影响。"
        elif "happiness_change" in choice_effects and choice_effects["happiness_change"] > 0:
            thought = "能够让自己更快乐的选择，通常是正确的方向。"
        elif "stress_change" in choice_effects and choice_effects["stress_change"] < 0:
            thought = "减少压力对身心健康都有好处，这是明智的选择。"
        else:
            thought = "虽然这个选择可能不是最优的，但也是当前情况下的合理决定。"

    "决策影响："
    for effect_type, effect_value in choice_effects.items():
        if effect_type == "cash_change":
            if effect_value > 0:
                "经济状况改善了¥[effect_value]"
            else:
                "经济支出增加了¥[-effect_value]"
        elif effect_type == "happiness_change":
            if effect_value > 0:
                "心情变好了[effect_value]点"
            else:
                "心情变差了[-effect_value]点"
        elif effect_type == "stress_change":
            if effect_value > 0:
                "压力增加了[effect_value]点"
            else:
                "压力减少了[-effect_value]点"
        elif effect_type == "health_change":
            if effect_value > 0:
                "健康状况改善了[effect_value]点"
            else:
                "健康状况下降了[-effect_value]点"
        elif effect_type == "energy_change":
            if effect_value > 0:
                "精力增加了[effect_value]点"
            else:
                "精力消耗了[-effect_value]点"

    "[selected_character.name]的想法："
    "「[thought]」"

    "分析：[outcome_analysis]"

    menu:
        "继续体验："

        "观察决策后的生活变化":
            python:
                import random
                life_changes = [
                    "这个决策让日常生活发生了一些微妙的变化。",
                    "周围的人对这个决策有不同的看法和反应。",
                    "时间会证明这个决策是否正确。",
                    "这次经历增加了人生阅历和智慧。",
                    "每个决策都是成长路上的重要一步。"
                ]
                change = random.choice(life_changes)

            "[change]"

        "反思决策过程":
            "回顾整个决策过程，可以学到很多关于人性和生活的道理。"
            "每个人都会面临各种选择，关键是要为自己的决定负责。"

        "返回角色体验":
            jump character_experience

    jump character_decisions

# NPC互动
label npc_interaction:
    scene bg black
    with fade

    $ available_npcs = get_random_npcs(5)

    if not available_npcs:
        "当前没有可互动的NPC。"
        jump daily_routine

    "可互动的NPC："

    python:
        npc_choices = []
        for npc in available_npcs:
            job_info = f"，职业：{npc.current_job.job_type.value}" if npc.current_job else "，无业"
            npc_choices.append((f"{npc.name} ({npc.age}岁, {npc.gender.value}{job_info})", npc))

    $ selected_npc = renpy.display_menu(npc_choices)

    "你选择了与 [selected_npc.name] 互动。"

    # 显示NPC详细信息
    python:
        npc_location = selected_npc.current_location if hasattr(selected_npc, 'current_location') else "未知"
        npc_cash = selected_npc.cash if hasattr(selected_npc, 'cash') else 0
        npc_happiness = selected_npc.daily_stats.happiness if hasattr(selected_npc.daily_stats, 'happiness') else 50

        # 根据NPC心情生成描述
        if npc_happiness > 80:
            mood_desc = "看起来心情很好"
        elif npc_happiness > 60:
            mood_desc = "看起来心情不错"
        elif npc_happiness > 40:
            mood_desc = "看起来情绪平静"
        elif npc_happiness > 20:
            mood_desc = "看起来有些忧郁"
        else:
            mood_desc = "看起来心情很差"

    "[selected_npc.name]，[selected_npc.age]岁，[selected_npc.gender.value]，[mood_desc]。"

    if hasattr(selected_npc, 'current_job') and selected_npc.current_job:
        "职业：[selected_npc.current_job.job_type.value]"
    else:
        "目前无业。"

    "位置：[npc_location]"

    menu:
        "选择互动方式："

        "闲聊":
            $ update_player_stats(energy_change=-5, happiness_change=10, stress_change=-5)

            python:
                import random
                chat_topics = [
                    "天气", "工作", "爱好", "城市生活", "未来计划",
                    "最近的新闻", "食物", "旅行", "电影", "音乐"
                ]
                topic = random.choice(chat_topics)

                chat_results = [
                    f"你们聊了聊{topic}，度过了愉快的时光。",
                    f"你们讨论了关于{topic}的话题，相谈甚欢。",
                    f"{selected_npc.name}分享了关于{topic}的有趣见解。",
                    f"你们就{topic}进行了深入交流，增进了了解。"
                ]
                chat_result = random.choice(chat_results)

            "[chat_result]"
            $ advance_time(1)

        "送礼物" if player_cash >= 50:
            $ update_player_stats(cash_change=-50, happiness_change=5)

            python:
                import random
                gift_reactions = [
                    f"{selected_npc.name}很喜欢你的礼物，表示感谢！",
                    f"{selected_npc.name}对你的礼物感到惊喜。",
                    f"{selected_npc.name}接受了你的礼物，看起来很高兴。",
                    f"{selected_npc.name}对你的慷慨表示赞赏。"
                ]
                reaction = random.choice(gift_reactions)

            "[reaction]"
            $ advance_time(0.5)

        "送礼物" if player_cash < 50:
            "你没有足够的钱送礼物。"

        "询问信息":
            menu:
                "询问什么信息？"

                "关于城市":
                    python:
                        import random
                        city_info = [
                            "这座城市最近发展得不错，新建了很多设施。",
                            "城市的北区是高档住宅区，南区则是商业中心。",
                            "这里的公园很美，特别是在春天。",
                            "城市的交通系统最近有所改善，但高峰期仍然拥堵。"
                        ]
                        info = random.choice(city_info)

                    "[selected_npc.name]: [info]"

                "关于工作机会":
                    python:
                        import random
                        job_info = [
                            "最近科技公司在招聘，薪资待遇不错。",
                            "服务业一直有职位空缺，但工作强度较大。",
                            "教育行业比较稳定，但入职门槛较高。",
                            "创业环境还可以，但竞争也很激烈。"
                        ]
                        info = random.choice(job_info)

                    "[selected_npc.name]: [info]"

                "关于其他NPC":
                    python:
                        import random
                        if len(available_npcs) > 1:
                            other_npcs = [npc for npc in available_npcs if npc != selected_npc]
                            other_npc = random.choice(other_npcs)
                            npc_info = [
                                f"{other_npc.name}最近在忙着工作。",
                                f"{other_npc.name}是个不错的人，我们偶尔会聊天。",
                                f"我不太了解{other_npc.name}，只是见过几次。",
                                f"{other_npc.name}在社区里很活跃。"
                            ]
                        else:
                            npc_info = [
                                "我对其他人不太了解。",
                                "这个社区里的人都很友善。",
                                "我刚搬来不久，还在认识新朋友。"
                            ]
                        info = random.choice(npc_info)

                    "[selected_npc.name]: [info]"

            $ advance_time(0.5)

        "邀请共进晚餐" if player_cash >= 100:
            $ update_player_stats(cash_change=-100, happiness_change=15, hunger_change=-20)

            "你邀请[selected_npc.name]共进晚餐，度过了愉快的时光。"
            "你们在餐厅聊了很多话题，增进了彼此的了解。"

            $ advance_time(2)

        "邀请共进晚餐" if player_cash < 100:
            "你没有足够的钱请客吃饭。"

    jump daily_routine

# 经济活动
label economy_activities:
    scene bg black
    with fade

    menu:
        "选择经济活动："

        "寻找工作机会":
            menu:
                "选择工作类型："

                "临时工作" if player_energy >= 15:
                    $ update_player_stats(energy_change=-15, stress_change=5, cash_change=80)

                    python:
                        import random
                        temp_jobs = [
                            "你在餐厅做了几小时服务员",
                            "你帮助搬运货物",
                            "你做了一些数据录入工作",
                            "你参与了市场调研活动",
                            "你帮助清洁办公室"
                        ]
                        job_desc = random.choice(temp_jobs)

                    "[job_desc]，赚取了一些收入。"
                    $ advance_time(3)

                "临时工作" if player_energy < 15:
                    "你太累了，无法工作。"

                "全职工作" if player_energy >= 25:
                    $ update_player_stats(energy_change=-25, stress_change=15, cash_change=200)

                    python:
                        import random
                        full_jobs = [
                            "你在办公室工作了一整天",
                            "你在商店担任销售员",
                            "你做了一天的技术支持工作",
                            "你参与了项目开发",
                            "你在教育机构工作"
                        ]
                        job_desc = random.choice(full_jobs)

                    "[job_desc]，获得了不错的收入。"
                    $ advance_time(8)

                "全职工作" if player_energy < 25:
                    "你太累了，无法承担全职工作。"

                "创业项目" if player_cash >= 500:
                    $ update_player_stats(cash_change=-500, stress_change=20, energy_change=-20)

                    python:
                        import random
                        # 创业成功率
                        success_rate = random.randint(1, 100)
                        if success_rate <= 30:  # 30%成功率
                            profit = random.randint(800, 1500)
                            update_player_stats(cash_change=profit, happiness_change=25)
                            result = f"创业项目成功！你获得了¥{profit}的收益。"
                        elif success_rate <= 60:  # 30%平本
                            profit = random.randint(400, 600)
                            update_player_stats(cash_change=profit, happiness_change=10)
                            result = f"创业项目基本持平，你获得了¥{profit}的收益。"
                        else:  # 40%失败
                            loss = random.randint(100, 300)
                            update_player_stats(cash_change=-loss, stress_change=10)
                            result = f"创业项目遇到困难，额外损失了¥{loss}。"

                    "你投资了一个创业项目..."
                    "[result]"
                    $ advance_time(6)

                "创业项目" if player_cash < 500:
                    "你没有足够的资金进行创业投资。"

        "购物消费":
            menu:
                "选择购物类型："

                "日用品" if player_cash >= 30:
                    $ update_player_stats(cash_change=-30, happiness_change=5, health_change=5)
                    "你购买了一些日用品，生活质量有所提升。"
                    $ advance_time(1)

                "日用品" if player_cash < 30:
                    "你没有足够的钱购买日用品。"

                "奢侈品" if player_cash >= 200:
                    $ update_player_stats(cash_change=-200, happiness_change=20, stress_change=-10)
                    "你购买了一些奢侈品，感到很满足。"
                    $ advance_time(2)

                "奢侈品" if player_cash < 200:
                    "你没有足够的钱购买奢侈品。"

                "投资理财产品" if player_cash >= 1000:
                    $ update_player_stats(cash_change=-1000, stress_change=5)

                    python:
                        import random
                        # 投资回报率
                        return_rate = random.randint(1, 100)
                        if return_rate <= 20:  # 20%高回报
                            profit = random.randint(200, 400)
                            update_player_stats(cash_change=profit, happiness_change=15)
                            result = f"投资获得高回报！你额外获得了¥{profit}。"
                        elif return_rate <= 70:  # 50%正常回报
                            profit = random.randint(50, 150)
                            update_player_stats(cash_change=profit, happiness_change=5)
                            result = f"投资获得正常回报，你额外获得了¥{profit}。"
                        else:  # 30%亏损
                            loss = random.randint(100, 300)
                            update_player_stats(cash_change=-loss, stress_change=15)
                            result = f"投资出现亏损，你损失了¥{loss}。"

                    "你购买了理财产品..."
                    "[result]"
                    $ advance_time(1)

                "投资理财产品" if player_cash < 1000:
                    "你没有足够的资金进行投资。"

        "查看市场信息":
            python:
                import random
                market_info = [
                    "科技股表现强劲，投资前景看好。",
                    "房地产市场趋于稳定，价格波动不大。",
                    "消费品行业增长稳定，适合长期投资。",
                    "新兴产业发展迅速，但风险较高。",
                    "传统制造业面临转型压力。",
                    "服务业持续扩张，就业机会增加。"
                ]

                selected_info = random.sample(market_info, 3)

            "当前市场信息："
            for info in selected_info:
                "• [info]"

            $ update_player_stats(happiness_change=3)
            $ advance_time(0.5)

        "银行服务":
            menu:
                "选择银行服务："

                "存款" if player_cash >= 100:
                    $ deposit_amount = min(player_cash - 50, 1000)  # 保留50元生活费
                    $ update_player_stats(cash_change=-deposit_amount)

                    "你存入了¥[deposit_amount]，获得了安全感。"
                    $ update_player_stats(stress_change=-5, happiness_change=5)
                    $ advance_time(0.5)

                "存款" if player_cash < 100:
                    "你的现金不足，无法进行存款。"

                "贷款申请":
                    python:
                        import random
                        # 贷款审批
                        approval_rate = random.randint(1, 100)
                        if approval_rate <= 60:  # 60%通过率
                            loan_amount = random.randint(500, 2000)
                            update_player_stats(cash_change=loan_amount, stress_change=10)
                            result = f"贷款申请通过！你获得了¥{loan_amount}的贷款。"
                        else:
                            result = "很抱歉，你的贷款申请未通过。"

                    "[result]"
                    $ advance_time(1)

    jump daily_routine

# 查看状态
label check_status:
    scene bg black
    with fade

    "玩家状态："
    "现金: ¥[player_cash:.2f]"
    "精力: [player_energy]/100"
    "心情: [player_happiness]/100"
    "压力: [player_stress]/100"
    "健康: [player_health]/100"
    "饥饿: [player_hunger]/100"

    "世界信息："
    $ time_str = time_system.get_current_time_info().get("time_string", "未知") if time_system else "未知"
    "当前时间: [time_str]"
    "世界名称: [current_world.name]" if current_world else "世界: 未知"

    "按任意键继续..."
    pause

    jump daily_routine

# 继续游戏
label continue_game:
    scene bg black
    with fade

    "加载游戏..."

    if current_world:
        "继续游戏..."
        jump daily_routine
    else:
        "没有找到保存的游戏。"
        jump game_main_menu

# 设置菜单
label settings_menu:
    scene bg black
    with fade

    call screen settings_screen

    if _return == "main_menu":
        jump game_main_menu
    else:
        jump settings_menu

# 设置界面
screen settings_screen():
    tag menu

    # 背景
    add "#0f1419"

    # 标题
    frame:
        xalign 0.5
        ypos 50
        padding (30, 15)
        background "#2c3e50"

        text "游戏设置" size 36 color "#ecf0f1" xalign 0.5

    # 设置内容区域
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 500
        padding (40, 40)
        background "#34495e"

        vbox:
            spacing 30

            # 音量设置
            vbox:
                spacing 15
                text "音量设置" size 24 color "#3498db"

                hbox:
                    spacing 20
                    text "主音量:" size 16 color "#ecf0f1" yalign 0.5
                    bar value Preference("music volume") xsize 300 ysize 20

                hbox:
                    spacing 20
                    text "音效音量:" size 16 color "#ecf0f1" yalign 0.5
                    bar value Preference("sound volume") xsize 300 ysize 20

                hbox:
                    spacing 20
                    text "语音音量:" size 16 color "#ecf0f1" yalign 0.5
                    bar value Preference("voice volume") xsize 300 ysize 20

            # 显示设置
            vbox:
                spacing 15
                text "显示设置" size 24 color "#3498db"

                hbox:
                    spacing 20
                    text "全屏模式:" size 16 color "#ecf0f1" yalign 0.5
                    textbutton "开启" action Preference("display", "fullscreen") text_size 16
                    textbutton "窗口" action Preference("display", "window") text_size 16

                hbox:
                    spacing 20
                    text "文本速度:" size 16 color "#ecf0f1" yalign 0.5
                    bar value Preference("text speed") xsize 300 ysize 20

                hbox:
                    spacing 20
                    text "自动前进时间:" size 16 color "#ecf0f1" yalign 0.5
                    bar value Preference("auto-forward time") xsize 300 ysize 20

            # 游戏设置
            vbox:
                spacing 15
                text "游戏设置" size 24 color "#3498db"

                hbox:
                    spacing 20
                    text "跳过未读文本:" size 16 color "#ecf0f1" yalign 0.5
                    textbutton "允许" action Preference("skip", "all") text_size 16
                    textbutton "禁止" action Preference("skip", "seen") text_size 16

    # 返回按钮
    textbutton "返回主菜单":
        xalign 0.5
        yalign 0.95
        action Return("main_menu")
        text_size 20
        xsize 200
        ysize 40
        text_color "#ecf0f1"
        text_hover_color "#e74c3c"
        background "#2c3e50"
        hover_background "#34495e"

# 主菜单界面
screen game_main_menu_screen():
    tag menu

    # 背景渐变
    add "#0f1419"

    # 背景装饰
    frame:
        xalign 0.5
        yalign 0.5
        xsize 1200
        ysize 800
        background "#1a252faa"

    # 主标题区域
    frame:
        xalign 0.5
        ypos 80
        padding (40, 25)
        background "#2c3e50"

        vbox:
            spacing 10
            text "人类繁荣" size 56 color "#ecf0f1" xalign 0.5 font "SourceHanSansLite.ttf"
            text "Human Flourishing" size 18 color "#95a5a6" xalign 0.5

    # 副标题和描述
    frame:
        xalign 0.5
        ypos 220
        padding (30, 15)
        background "#34495e"

        vbox:
            spacing 8
            text "社会模拟游戏" size 24 color "#3498db" xalign 0.5
            text "体验一个小型社会的成长与发展" size 14 color "#bdc3c7" xalign 0.5

    # 主菜单按钮区域
    frame:
        xalign 0.5
        ypos 320
        padding (40, 30)
        background "#2c3e50aa"

        vbox:
            spacing 25

            # 新游戏按钮
            textbutton "开始新游戏":
                action Return("new_game")
                text_size 28
                xsize 300
                ysize 60
                text_color "#ecf0f1"
                text_hover_color "#3498db"
                background "#e74c3c"
                hover_background "#c0392b"

            # 继续游戏按钮
            textbutton "继续游戏":
                action Return("continue")
                text_size 24
                xsize 300
                ysize 50
                text_color "#ecf0f1"
                text_hover_color "#2ecc71"
                background "#27ae60"
                hover_background "#229954"

            # 世界浏览按钮
            textbutton "浏览世界":
                action Return("browse_world")
                text_size 24
                xsize 300
                ysize 50
                text_color "#ecf0f1"
                text_hover_color "#f39c12"
                background "#f39c12"
                hover_background "#e67e22"

            # 设置按钮
            textbutton "游戏设置":
                action Return("settings")
                text_size 20
                xsize 300
                ysize 45
                text_color "#ecf0f1"
                text_hover_color "#9b59b6"
                background "#8e44ad"
                hover_background "#7d3c98"

            # 退出按钮
            textbutton "退出游戏":
                action Return("exit")
                text_size 18
                xsize 300
                ysize 40
                text_color "#ecf0f1"
                text_hover_color "#e74c3c"
                background "#95a5a6"
                hover_background "#7f8c8d"

    # 游戏信息面板
    frame:
        xpos 50
        ypos 500
        padding (20, 15)
        background "#34495eaa"

        vbox:
            spacing 10
            text "游戏特色" size 18 color "#3498db"
            text "• 动态NPC系统" size 12 color "#bdc3c7"
            text "• 实时经济模拟" size 12 color "#bdc3c7"
            text "• 社会发展追踪" size 12 color "#bdc3c7"
            text "• 多层次互动" size 12 color "#bdc3c7"

    # 统计信息面板
    frame:
        xalign 1.0
        ypos 500
        xoffset -50
        padding (20, 15)
        background "#34495eaa"

        vbox:
            spacing 10
            text "世界状态" size 18 color "#f39c12"
            if current_world:
                text "世界: [current_world.name]" size 12 color "#ecf0f1"
                $ npc_count = len(db_manager.load_all_active_npcs()) if db_manager else 0
                text "NPC数量: [npc_count]" size 12 color "#ecf0f1"
                $ time_str = time_system.get_current_time_info().get("time_string", "未知") if time_system else "未知"
                text "时间: [time_str]" size 12 color "#ecf0f1"
            else:
                text "暂无世界" size 12 color "#95a5a6"
                text "请创建新游戏" size 12 color "#95a5a6"

    # 版本和开发信息
    frame:
        xalign 1.0
        yalign 1.0
        xoffset -20
        yoffset -20
        padding (15, 10)
        background "#2c3e50aa"

        vbox:
            spacing 5
            text "版本 1.0 - 开发中" size 12 color "#7f8c8d" xalign 1.0
            text "基于Ren'Py引擎" size 10 color "#95a5a6" xalign 1.0

# 游戏状态显示界面
screen game_status_hud():
    # 状态栏
    frame:
        xpos 20
        ypos 20
        padding (15, 10)
        background "#2c3e50aa"

        hbox:
            spacing 20

            vbox:
                spacing 5
                text "现金" size 12 color "#f39c12"
                text "¥[player_cash:.0f]" size 14 color "#ecf0f1"

            vbox:
                spacing 5
                text "精力" size 12 color "#e74c3c"
                text "[player_energy]/100" size 14 color "#ecf0f1"

            vbox:
                spacing 5
                text "心情" size 12 color "#3498db"
                text "[player_happiness]/100" size 14 color "#ecf0f1"

            vbox:
                spacing 5
                text "时间" size 12 color "#9b59b6"
                $ time_str = time_system.get_current_time_info().get("time_string", "未知") if time_system else "未知"
                text "[time_str]" size 14 color "#ecf0f1"

# 快速菜单界面
screen quick_menu():
    hbox:
        xalign 1.0
        yalign 1.0
        xoffset -20
        yoffset -20
        spacing 10

        textbutton "状态" action Jump("check_status") text_size 12
        textbutton "世界" action Jump("world_overview") text_size 12
        textbutton "菜单" action Jump("game_main_menu") text_size 12

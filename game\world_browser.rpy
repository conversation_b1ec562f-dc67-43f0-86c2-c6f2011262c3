# -*- coding: utf-8 -*-
# 世界浏览器界面

# 世界浏览器主屏幕
screen world_browser_screen():
    tag menu

    # 背景
    add "#1a252f"

    # 标题栏
    frame:
        xalign 0.5
        ypos 10
        xsize 1200
        padding (20, 15)
        background "#2c3e50"

        hbox:
            spacing 30
            text "[current_world.name] - 世界浏览器" size 32 color "#ecf0f1"
            $ time_str = time_system.get_current_time_info().get("time_string", "未知") if time_system else "未知"
            text "时间: [time_str]" size 16 color "#95a5a6"

    # 主要内容区域
    hbox:
        xalign 0.5
        ypos 80
        spacing 20

        # 左侧：世界信息和控制面板
        vbox:
            spacing 20

            # 世界基本信息
            frame:
                xsize 380
                ysize 280
                padding (20, 20)
                background "#34495e"

                vbox:
                    spacing 12

                    text "世界概况" size 24 color "#3498db"

                    hbox:
                        spacing 15
                        vbox:
                            spacing 8
                            text "基本信息" size 16 color "#f39c12"
                            text "名称: [current_world.name]" size 14 color "#ecf0f1"
                            text "版本: [current_world.version]" size 14 color "#ecf0f1"
                            text "第[current_world.current_day]天" size 14 color "#ecf0f1"
                            text "[current_world.current_season] [current_world.current_year]年" size 14 color "#ecf0f1"

                        vbox:
                            spacing 8
                            text "人口统计" size 16 color "#e74c3c"
                            text "总人口: [current_world.total_population:,]" size 14 color "#ecf0f1"
                            $ active_npcs = len(db_manager.load_all_active_npcs()) if db_manager else 0
                            text "活跃NPC: [active_npcs]" size 14 color "#ecf0f1"
                            $ employment_rate = (active_npcs * 0.7) if active_npcs > 0 else 0
                            text "就业率: [employment_rate:.0f]%" size 14 color "#ecf0f1"

                    text "经济指标" size 16 color "#27ae60"
                    hbox:
                        spacing 20
                        vbox:
                            spacing 5
                            text "货币: [current_world.base_currency]" size 14 color "#ecf0f1"
                            text "通胀: [current_world.global_inflation_rate:.2%]" size 14 color "#ecf0f1"
                        vbox:
                            spacing 5
                            text "增长: [current_world.global_growth_rate:.2%]" size 14 color "#ecf0f1"
                            $ gdp_estimate = current_world.total_population * 50000
                            text "GDP: ¥[gdp_estimate:,.0f]" size 14 color "#ecf0f1"

            # 快速操作面板
            frame:
                xsize 380
                ysize 200
                padding (20, 20)
                background "#34495e"

                vbox:
                    spacing 15

                    text "快速操作" size 20 color "#9b59b6"

                    hbox:
                        spacing 10
                        textbutton "生成报告":
                            action Function(generate_world_report)
                            text_size 14
                            xsize 110
                            ysize 35
                            text_color "#ecf0f1"
                            background "#8e44ad"
                            hover_background "#7d3c98"

                        textbutton "经济分析":
                            action Function(show_economic_analysis)
                            text_size 14
                            xsize 110
                            ysize 35
                            text_color "#ecf0f1"
                            background "#27ae60"
                            hover_background "#229954"

                        textbutton "人口统计":
                            action Function(show_population_stats)
                            text_size 14
                            xsize 110
                            ysize 35
                            text_color "#ecf0f1"
                            background "#e74c3c"
                            hover_background "#c0392b"

                    hbox:
                        spacing 10
                        textbutton "时间控制":
                            action Function(show_time_controls)
                            text_size 14
                            xsize 110
                            ysize 35
                            text_color "#ecf0f1"
                            background "#f39c12"
                            hover_background "#e67e22"

                        textbutton "事件日志":
                            action Function(show_event_log)
                            text_size 14
                            xsize 110
                            ysize 35
                            text_color "#ecf0f1"
                            background "#34495e"
                            hover_background "#2c3e50"

                        textbutton "设置":
                            action Function(show_world_settings)
                            text_size 14
                            xsize 110
                            ysize 35
                            text_color "#ecf0f1"
                            background "#95a5a6"
                            hover_background "#7f8c8d"
        
        # 右侧：区域浏览和详细信息面板
        vbox:
            spacing 20

            # 区域浏览面板
            frame:
                xsize 700
                ysize 400
                padding (20, 20)
                background "#34495e"

                vbox:
                    spacing 15

                    hbox:
                        text "区域浏览" size 24 color "#3498db"
                        null width 20
                        if current_region:
                            textbutton "返回上级":
                                action Function(navigate_up)
                                text_size 14
                                xsize 80
                                ysize 30
                                text_color "#ecf0f1"
                                background "#e74c3c"
                                hover_background "#c0392b"

                        null width 20
                        textbutton "刷新":
                            action Function(refresh_world_data)
                            text_size 14
                            xsize 60
                            ysize 30
                            text_color "#ecf0f1"
                            background "#27ae60"
                            hover_background "#229954"

                    # 当前路径和统计信息
                    frame:
                        padding (15, 10)
                        background "#2c3e50"
                        xsize 660

                        hbox:
                            spacing 30
                            vbox:
                                spacing 5
                                if current_region:
                                    text "当前位置: [get_region_path()]" size 14 color "#f39c12"
                                    text "区域类型: [current_region.region_type.value]" size 12 color "#bdc3c7"
                                else:
                                    text "当前位置: 世界根目录" size 14 color "#f39c12"
                                    text "显示: 顶级行政区域" size 12 color "#bdc3c7"

                            vbox:
                                spacing 5
                                python:
                                    if current_region is None:
                                        regions_to_show = db_manager.load_regions_by_parent(None) if db_manager else []
                                        region_count = len(regions_to_show)
                                        population_in_region = sum(getattr(r, 'population', 0) for r in regions_to_show)
                                    else:
                                        regions_to_show = db_manager.load_regions_by_parent(current_region.id) if db_manager else []
                                        region_count = len(regions_to_show)
                                        population_in_region = getattr(current_region, 'population', 0)

                                text "子区域数: [region_count]" size 12 color "#ecf0f1"
                                text "区域人口: [population_in_region:,]" size 12 color "#ecf0f1"

                    # 区域列表
                    viewport:
                        scrollbars "vertical"
                        mousewheel True
                        ysize 280
                        xsize 660

                        vbox:
                            spacing 8

                            if regions_to_show:
                                for region in regions_to_show:
                                    frame:
                                        padding (15, 12)
                                        background "#2c3e50"
                                        hover_background "#34495e"
                                        xsize 640

                                        hbox:
                                            spacing 20

                                            # 区域基本信息
                                            vbox:
                                                spacing 5
                                                textbutton "[region.name]":
                                                    action Function(navigate_to_region, region)
                                                    text_size 16
                                                    text_color "#3498db"
                                                    text_hover_color "#2980b9"
                                                    background None

                                                text "[region.region_type.value]" size 12 color "#95a5a6"

                                                python:
                                                    region_pop = getattr(region, 'population', 0)
                                                    region_area = getattr(region, 'area', 0)

                                                if region_pop > 0:
                                                    text "人口: [region_pop:,]" size 12 color "#ecf0f1"
                                                if region_area > 0:
                                                    text "面积: [region_area] km²" size 12 color "#ecf0f1"

                                            # 区域状态指示器
                                            vbox:
                                                spacing 5
                                                xalign 1.0

                                                python:
                                                    # 计算区域发展水平
                                                    development_level = min(100, (region_pop / 10000) * 100) if region_pop > 0 else 0

                                                    # 经济活跃度
                                                    economic_activity = min(100, development_level * 1.2)

                                                    # 区域稳定性
                                                    stability = max(60, min(100, 100 - (development_level * 0.3)))

                                                text "发展: [development_level:.0f]%" size 10 color "#27ae60"
                                                text "经济: [economic_activity:.0f]%" size 10 color "#f39c12"
                                                text "稳定: [stability:.0f]%" size 10 color "#3498db"

                                                # 快速操作按钮
                                                hbox:
                                                    spacing 5
                                                    textbutton "详情":
                                                        action Function(show_region_details, region)
                                                        text_size 10
                                                        xsize 40
                                                        ysize 20
                                                        text_color "#ecf0f1"
                                                        background "#8e44ad"
                                                        hover_background "#7d3c98"

                                                    textbutton "NPC":
                                                        action Function(show_region_npcs, region)
                                                        text_size 10
                                                        xsize 40
                                                        ysize 20
                                                        text_color "#ecf0f1"
                                                        background "#e74c3c"
                                                        hover_background "#c0392b"
                            else:
                                text "此区域没有子区域" size 16 color "#95a5a6" xalign 0.5 yalign 0.5

            # 详细信息面板
            frame:
                xsize 700
                ysize 160
                padding (20, 15)
                background "#34495e"

                vbox:
                    spacing 10

                    text "区域详细信息" size 18 color "#e74c3c"

                    if selected_region_info:
                        text "[selected_region_info]" size 14 color "#ecf0f1"
                    else:
                        text "点击区域名称查看详细信息，或使用上方的快速操作按钮。" size 14 color "#95a5a6"

                    hbox:
                        spacing 15
                        textbutton "返回游戏":
                            action Return("daily_routine")
                            text_size 16
                            xsize 100
                            ysize 35
                            text_color "#ecf0f1"
                            background "#27ae60"
                            hover_background "#229954"

                        textbutton "角色模式":
                            action Return("character_mode")
                            text_size 16
                            xsize 100
                            ysize 35
                            text_color "#ecf0f1"
                            background "#3498db"
                            hover_background "#2980b9"

                        textbutton "NPC管理":
                            action Return("npc_manager")
                            text_size 16
                            xsize 100
                            ysize 35
                            text_color "#ecf0f1"
                            background "#f39c12"
                            hover_background "#e67e22"
                                regions_to_show = db_manager.load_regions_by_parent(current_region.id)
                                # 如果是城市级别，还要显示区块
                                if current_region.type.value == "city":
                                    current_blocks = db_manager.load_blocks_by_parent(current_region.id)
                                else:
                                    current_blocks = []
                        
                        # 显示子区域
                        for region in regions_to_show:
                            frame:
                                padding (10, 5)
                                background "#2c3e50"
                                xfill True
                                
                                hbox:
                                    spacing 10
                                    
                                    # 区域类型图标
                                    text get_region_icon(region.type.value) size 20
                                    
                                    vbox:
                                        spacing 2
                                        
                                        hbox:
                                            text "[region.name]" size 18 color "#ecf0f1"
                                            null width 10
                                            text "([region.type.value])" size 14 color "#95a5a6"
                                        
                                        text "人口: [region.population:,]" size 14 color "#bdc3c7"
                                        if hasattr(region, 'gdp') and region.gdp > 0:
                                            text "GDP: [region.gdp:,.0f]" size 14 color "#bdc3c7"
                                    
                                    null width 20
                                    
                                    textbutton "进入" action Function(navigate_to_region, region) text_size 14
                        
                        # 显示区块（如果当前在城市级别）
                        for block in current_blocks:
                            frame:
                                padding (10, 5)
                                background "#1a252f"
                                xfill True
                                
                                hbox:
                                    spacing 10
                                    
                                    # 区块类型图标
                                    text get_block_icon(block.type.value) size 20
                                    
                                    vbox:
                                        spacing 2
                                        
                                        hbox:
                                            text "[block.name]" size 18 color "#ecf0f1"
                                            null width 10
                                            text "([block.type.value])" size 14 color "#95a5a6"
                                        
                                        text "人口: [block.population:,]" size 14 color "#bdc3c7"
                                        text "面积: [block.land_area:.1f] km²" size 14 color "#bdc3c7"
                                        text "发展度: [block.development_level:.1f]" size 14 color "#bdc3c7"
                                    
                                    null width 20
                                    
                                    textbutton "详情" action Function(show_block_details, block) text_size 14
    
    # 底部按钮栏
    hbox:
        xalign 0.5
        ypos 650
        spacing 20

        textbutton "NPC管理" action Jump("npc_manager") text_size 16
        textbutton "新闻中心" action Jump("news_system") text_size 16
        textbutton "角色模式" action Jump("character_selection") text_size 16
        textbutton "上帝模式" action Jump("god_mode_editor") text_size 16
        textbutton "多媒体中心" action Jump("multimedia_center") text_size 16
        textbutton "生成新世界" action Jump("world_creation") text_size 16
        textbutton "保存世界" action Function(save_current_world) text_size 16
        textbutton "退出游戏" action Quit() text_size 16

# 导航函数
init python:
    def navigate_to_region(region):
        global current_region
        current_region = region
        renpy.restart_interaction()
    
    def navigate_up():
        global current_region
        if current_region and current_region.parent_id:
            current_region = db_manager.get_region_by_id(current_region.parent_id)
        else:
            current_region = None
        renpy.restart_interaction()
    
    def get_region_path():
        if not current_region:
            return "世界"
        
        path_parts = []
        region = current_region
        while region:
            path_parts.append(region.name)
            if region.parent_id:
                region = db_manager.get_region_by_id(region.parent_id)
            else:
                break
        
        path_parts.reverse()
        return " > ".join(path_parts)
    
    def get_region_icon(region_type):
        icons = {
            "country": "🏛️",
            "province": "🏞️", 
            "city": "🏙️",
            "district": "🏢",
            "village": "🏘️"
        }
        return icons.get(region_type, "📍")
    
    def get_block_icon(block_type):
        icons = {
            "district": "🏢",
            "village": "🏘️"
        }
        return icons.get(block_type, "📍")
    
    def show_block_details(block):
        # 显示区块详细信息的对话框
        renpy.call_screen("block_details_screen", block=block)
    
    def save_current_world():
        if current_world:
            success = db_manager.save_world(current_world)
            if success:
                renpy.notify("世界保存成功！")
            else:
                renpy.notify("世界保存失败！")

# 区块详情屏幕
screen block_details_screen(block):
    modal True
    
    # 半透明背景
    add "#000000aa"
    
    # 详情面板
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        padding (30, 30)
        background "#34495e"
        
        vbox:
            spacing 15
            
            text "[block.name] 详细信息" size 24 color "#ecf0f1" xalign 0.5
            
            null height 10
            
            text "基本信息" size 18 color "#ecf0f1"
            text "类型: [block.type.value]" size 16 color "#bdc3c7"
            text "所属: [block.parent_id]" size 16 color "#bdc3c7"
            text "面积: [block.land_area:.2f] 平方公里" size 16 color "#bdc3c7"
            
            null height 10
            
            text "人口与建设" size 18 color "#ecf0f1"
            text "当前人口: [block.population:,]" size 16 color "#bdc3c7"
            text "人口上限: [block.population_cap:,]" size 16 color "#bdc3c7"
            text "建筑容量: [block.buildable_index_used:.1f] / [block.buildable_index_max:.1f]" size 16 color "#bdc3c7"
            
            null height 10
            
            text "经济信息" size 18 color "#ecf0f1"
            text "地价: [block.land_price:,.0f] / m²" size 16 color "#bdc3c7"
            text "发展水平: [block.development_level:.2f]" size 16 color "#bdc3c7"
            text "基础设施: [block.infrastructure_score:.1f]" size 16 color "#bdc3c7"
            
            null height 20
            
            textbutton "关闭" action Return() xalign 0.5 text_size 16

# 新增功能函数
init python:
    selected_region_info = None

    def refresh_world_data():
        """刷新世界数据"""
        global db_manager
        if db_manager:
            try:
                # 重新加载当前世界数据
                existing_worlds = db_manager.list_worlds()
                if existing_worlds:
                    global current_world
                    current_world = db_manager.load_world(existing_worlds[0]['id'])
                renpy.notify("世界数据已刷新")
            except Exception as e:
                renpy.notify(f"刷新失败: {e}")
        renpy.restart_interaction()

    def show_region_details(region):
        """显示区域详细信息"""
        global selected_region_info

        try:
            # 获取区域的详细信息
            region_pop = getattr(region, 'population', 0)
            region_area = getattr(region, 'area', 0)
            region_type = region.type.value

            # 计算一些统计数据
            development_level = min(100, (region_pop / 10000) * 100) if region_pop > 0 else 0
            economic_activity = min(100, development_level * 1.2)

            # 获取子区域数量
            child_regions = db_manager.load_regions_by_parent(region.id) if db_manager else []
            child_count = len(child_regions)

            # 构建详细信息文本
            info_parts = [
                f"区域名称: {region.name}",
                f"区域类型: {region_type}",
                f"人口数量: {region_pop:,}人" if region_pop > 0 else "人口数量: 未知",
                f"区域面积: {region_area} km²" if region_area > 0 else "区域面积: 未知",
                f"发展水平: {development_level:.1f}%",
                f"经济活跃度: {economic_activity:.1f}%",
                f"下级区域: {child_count}个"
            ]

            selected_region_info = " | ".join(info_parts)

        except Exception as e:
            selected_region_info = f"获取区域信息失败: {e}"

        renpy.restart_interaction()

    def show_region_npcs(region):
        """显示区域内的NPC"""
        global selected_region_info

        try:
            # 获取该区域的NPC（这里简化处理）
            all_npcs = db_manager.load_all_active_npcs() if db_manager else []

            # 过滤出在该区域的NPC（简化逻辑）
            region_npcs = []
            for npc in all_npcs[:10]:  # 限制显示数量
                if hasattr(npc, 'current_location'):
                    region_npcs.append(npc)

            if region_npcs:
                npc_names = [f"{npc.name}({npc.age}岁)" for npc in region_npcs[:5]]
                npc_info = "区域内NPC: " + ", ".join(npc_names)
                if len(region_npcs) > 5:
                    npc_info += f" 等{len(region_npcs)}人"
            else:
                npc_info = "该区域暂无活跃NPC"

            selected_region_info = npc_info

        except Exception as e:
            selected_region_info = f"获取NPC信息失败: {e}"

        renpy.restart_interaction()

    def generate_world_report():
        """生成世界报告"""
        global selected_region_info

        try:
            # 获取基本统计信息
            all_npcs = db_manager.load_all_active_npcs() if db_manager else []
            active_npc_count = len(all_npcs)

            # 计算就业统计
            employed_npcs = [npc for npc in all_npcs if hasattr(npc, 'current_job') and npc.current_job]
            employment_rate = (len(employed_npcs) / active_npc_count * 100) if active_npc_count > 0 else 0

            # 计算平均年龄
            if all_npcs:
                avg_age = sum(npc.age for npc in all_npcs) / len(all_npcs)
            else:
                avg_age = 0

            # 性别分布
            male_count = len([npc for npc in all_npcs if npc.gender.value == "男性"])
            female_count = len([npc for npc in all_npcs if npc.gender.value == "女性"])

            report_parts = [
                f"世界报告 - {current_world.name}",
                f"总人口: {current_world.total_population:,}",
                f"活跃NPC: {active_npc_count}",
                f"就业率: {employment_rate:.1f}%",
                f"平均年龄: {avg_age:.1f}岁",
                f"性别比例: 男{male_count}:女{female_count}"
            ]

            selected_region_info = " | ".join(report_parts)

        except Exception as e:
            selected_region_info = f"生成报告失败: {e}"

        renpy.restart_interaction()

    def show_economic_analysis():
        """显示经济分析"""
        global selected_region_info

        try:
            # 获取经济数据
            inflation_rate = current_world.global_inflation_rate
            growth_rate = current_world.global_growth_rate

            # 估算GDP和其他经济指标
            estimated_gdp = current_world.total_population * 50000
            per_capita_income = estimated_gdp / current_world.total_population if current_world.total_population > 0 else 0

            # 获取NPC财富统计
            all_npcs = db_manager.load_all_active_npcs() if db_manager else []
            if all_npcs:
                total_wealth = sum(getattr(npc, 'cash', 0) for npc in all_npcs)
                avg_wealth = total_wealth / len(all_npcs)
            else:
                total_wealth = 0
                avg_wealth = 0

            analysis_parts = [
                f"经济分析",
                f"GDP: ¥{estimated_gdp:,.0f}",
                f"人均收入: ¥{per_capita_income:,.0f}",
                f"通胀率: {inflation_rate:.2%}",
                f"增长率: {growth_rate:.2%}",
                f"NPC平均财富: ¥{avg_wealth:.0f}"
            ]

            selected_region_info = " | ".join(analysis_parts)

        except Exception as e:
            selected_region_info = f"经济分析失败: {e}"

        renpy.restart_interaction()

    def show_population_stats():
        """显示人口统计"""
        global selected_region_info

        try:
            all_npcs = db_manager.load_all_active_npcs() if db_manager else []

            if not all_npcs:
                selected_region_info = "人口统计: 暂无活跃NPC数据"
                renpy.restart_interaction()
                return

            # 年龄分布
            age_groups = {"青年(18-30)": 0, "中年(31-50)": 0, "老年(51+)": 0}
            for npc in all_npcs:
                if npc.age <= 30:
                    age_groups["青年(18-30)"] += 1
                elif npc.age <= 50:
                    age_groups["中年(31-50)"] += 1
                else:
                    age_groups["老年(51+)"] += 1

            # 职业分布
            job_stats = {}
            for npc in all_npcs:
                if hasattr(npc, 'current_job') and npc.current_job:
                    job_type = npc.current_job.job_type.value
                    job_stats[job_type] = job_stats.get(job_type, 0) + 1
                else:
                    job_stats["无业"] = job_stats.get("无业", 0) + 1

            # 构建统计信息
            stats_parts = [
                f"人口统计 - 总计{len(all_npcs)}人"
            ]

            # 年龄分布
            for age_group, count in age_groups.items():
                percentage = (count / len(all_npcs)) * 100
                stats_parts.append(f"{age_group}: {count}人({percentage:.1f}%)")

            selected_region_info = " | ".join(stats_parts)

        except Exception as e:
            selected_region_info = f"人口统计失败: {e}"

        renpy.restart_interaction()

    def show_time_controls():
        """显示时间控制信息"""
        global selected_region_info

        try:
            current_time_info = time_system.get_current_time_info() if time_system else {}
            time_str = current_time_info.get("time_string", "未知时间")
            day = current_time_info.get("day", 1)
            hour = current_time_info.get("hour", 8)
            season = current_time_info.get("season", "春季")
            year = current_time_info.get("year", 1)

            time_info_parts = [
                f"时间控制",
                f"当前时间: {time_str}",
                f"第{day}天 {hour}:00",
                f"{season} {year}年",
                "可在上帝模式中调整时间"
            ]

            selected_region_info = " | ".join(time_info_parts)

        except Exception as e:
            selected_region_info = f"时间信息获取失败: {e}"

        renpy.restart_interaction()

    def show_event_log():
        """显示事件日志"""
        global selected_region_info

        try:
            # 这里可以实现真正的事件日志系统
            # 目前显示模拟的事件信息
            import random

            sample_events = [
                "新NPC加入社区",
                "经济指标更新",
                "季节变化",
                "人口增长",
                "区域发展",
                "市场波动"
            ]

            recent_events = random.sample(sample_events, min(3, len(sample_events)))

            event_info_parts = [
                "最近事件"
            ] + [f"• {event}" for event in recent_events]

            selected_region_info = " | ".join(event_info_parts)

        except Exception as e:
            selected_region_info = f"事件日志获取失败: {e}"

        renpy.restart_interaction()

    def show_world_settings():
        """显示世界设置"""
        global selected_region_info

        try:
            settings_info = [
                "世界设置",
                f"基础货币: {current_world.base_currency}",
                f"通胀率: {current_world.global_inflation_rate:.2%}",
                f"增长率: {current_world.global_growth_rate:.2%}",
                "可在上帝模式中修改设置"
            ]

            selected_region_info = " | ".join(settings_info)

        except Exception as e:
            selected_region_info = f"设置信息获取失败: {e}"

        renpy.restart_interaction()
